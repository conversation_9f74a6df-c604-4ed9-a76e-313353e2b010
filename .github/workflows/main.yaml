name: main

on:
  push:
    branches:
      - "master"
    tags:
      - "v*"
  pull_request:

jobs:
  # linters
  lint-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          package_json_file: "frontend/package.json"
      - uses: actions/setup-node@v4
        with:
          node-version: "22.x"
          cache: "pnpm"
          cache-dependency-path: "frontend/pnpm-lock.yaml"
      - run: make lint-frontend
  lint-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23.0
      - run: make lint-backend
  lint:
    runs-on: ubuntu-latest
    needs: [lint-frontend, lint-backend]
    steps:
      - run: echo "done"

  # tests
  test-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          package_json_file: "frontend/package.json"
      - uses: actions/setup-node@v4
        with:
          node-version: "22.x"
          cache: "pnpm"
          cache-dependency-path: "frontend/pnpm-lock.yaml"
      - run: make test-frontend
  test-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23.0
      - run: make test-backend
  test:
    runs-on: ubuntu-latest
    needs: [test-frontend, test-backend]
    steps:
      - run: echo "done"

  # release
  release:
    runs-on: ubuntu-latest
    needs: [lint, test]
    if: startsWith(github.event.ref, 'refs/tags/v')
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23.0
      - uses: pnpm/action-setup@v4
        with:
          package_json_file: "frontend/package.json"
      - uses: actions/setup-node@v4
        with:
          node-version: "22.x"
          cache: "pnpm"
          cache-dependency-path: "frontend/pnpm-lock.yaml"
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Build frontend
        run: make build-frontend
      - name: Login to Docker Hub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v2
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
