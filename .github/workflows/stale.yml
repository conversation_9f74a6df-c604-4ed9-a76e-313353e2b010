name: 'Close stale issues and PRs'
permissions:
  issues: write
  pull-requests: write

on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          stale-pr-message: 'This PR is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 5 days.'
          close-pr-message: 'This PR was closed because it has been stalled for 5 days with no activity.'
          stale-issue-message: 'This issue is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 5 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 5 days with no activity.'
          days-before-stale: 30
          days-before-close: 5
          exempt-issue-labels: 'feature ☘,enhancement ⚙,bug 🐞'
          exempt-pr-labels: 'need-help,wip'
          operations-per-run: 100