linters-settings:
  dupl:
    threshold: 100
  exhaustive:
    default-signifies-exhaustive: false
  funlen:
    lines: 100
    statements: 50
  goconst:
    min-len: 2
    min-occurrences: 2
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport # https://github.com/go-critic/go-critic/issues/845
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc
  gocyclo:
    min-complexity: 15
  goimports:
    local-prefixes: github.com/puryagh/filebrowser
  gomnd:
    # don't include the "operation" and "assign"
    checks:
      - argument
      - case
      - condition
      - return
    ignored-numbers:
      - '0'
      - '1'
      - '2'
      - '3'
    ignored-functions:
      - strings.SplitN
  govet:
    enable:
      - nilness
      - shadow
  lll:
    line-length: 140
  misspell:
    locale: US
  nolintlint:
    allow-unused: false # report any unused nolint directives
    require-explanation: false # require an explanation for nolint directives
    require-specific: true # require nolint directives to be specific about which linter is being skipped

linters:
  # please, do not use `enable-all`: it's deprecated and will be removed soon.
  # inverted configuration with `enable-all` and `disable` is not scalable during updates of golangci-lint
  disable-all: true
  enable:
    - bodyclose
    - dogsled
    - dupl
    - errcheck
    - errorlint
    - exportloopref
    - exhaustive
    - funlen
    - gocheckcompilerdirectives
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - godox
    - goimports
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - nolintlint
    - prealloc
    - revive
    - rowserrcheck
    - staticcheck
    - stylecheck
    - testifylint
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace

issues:
  exclude-dirs:
    - frontend/
  exclude-rules:
    - path: cmd/.*.go
      linters:
        - gochecknoinits
    - path: .*_test.go
      linters:
        - lll
        - gochecknoinits
        - gocyclo
        - funlen
        - dupl
        - scopelint
    - text: "Auther"
      linters:
        - misspell
    - text: "strconv.Parse"
      linters:
        - gomnd

run:
  timeout: 5m