# LiveUtil File Browser

<p align="center">
  <img src="https://raw.githubusercontent.com/filebrowser/logo/master/banner.png" width="550"/>
</p>

[![Build](https://github.com/puryagh/filebrowser/actions/workflows/main.yaml/badge.svg)](https://github.com/puryagh/filebrowser/actions/workflows/main.yaml)
[![Go Report Card](https://goreportcard.com/badge/github.com/puryagh/filebrowser?style=flat-square)](https://goreportcard.com/report/github.com/puryagh/filebrowser)
[![Documentation](https://img.shields.io/badge/godoc-reference-blue.svg?style=flat-square)](http://godoc.org/github.com/puryagh/filebrowser)
[![Version](https://img.shields.io/github/release/puryagh/filebrowser.svg?style=flat-square)](https://github.com/puryagh/filebrowser/releases/latest)

LiveUtil File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename and edit your files. It allows the creation of multiple users and each user can have its own directory. It can be used as a standalone app.

## Demo

url: https://demo.liveutil.com/

credentials: `demo`/`demo`

## Features

Please refer to our docs at [https://liveutil.com/features](https://liveutil.com/features)

## Install

For installation instructions please refer to our docs at [https://liveutil.com/installation](https://liveutil.com/installation).

## Configuration

[Authentication Method](https://liveutil.com/configuration/authentication-method) - You can change the way the user authenticates with the filebrowser server

[Command Runner](https://liveutil.com/configuration/command-runner) - The command runner is a feature that enables you to execute any shell command you want before or after a certain event.

[Custom Branding](https://liveutil.com/configuration/custom-branding) - You can customize your LiveUtil File Browser installation by changing its name to any other you want, by adding a global custom style sheet and by using your own logotype if you want.

## Contributing

If you're interested in contributing to this project, our docs are best places to start [https://liveutil.com/contributing](https://liveutil.com/contributing).
