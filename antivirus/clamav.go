package antivirus

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"time"
)

var (
	ErrConnectionFailed = errors.New("failed to connect to ClamAV")
	ErrScanFailed       = errors.New("scan failed")
	ErrTimeout          = errors.New("scan timeout")
)

type ScanResult struct {
	Infected bool
	Virus    string
	Error    error
}

type ClamAVClient struct {
	conn    net.Conn
	timeout time.Duration
}

func NewClamAVClient(address string, timeout time.Duration) (*ClamAVClient, error) {
	conn, err := net.Dial("tcp", address)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrConnectionFailed, err)
	}
	return &ClamAVClient{
		conn:    conn,
		timeout: timeout,
	}, nil
}

func (c *ClamAVClient) ScanStream(ctx context.Context, reader io.Reader) (*ScanResult, error) {
	// Set deadline for the scan
	deadline := time.Now().Add(c.timeout)
	if err := c.conn.SetDeadline(deadline); err != nil {
		return nil, fmt.Errorf("failed to set deadline: %w", err)
	}

	// Send INSTREAM command
	if _, err := fmt.Fprintf(c.conn, "zINSTREAM\000"); err != nil {
		return nil, fmt.Errorf("failed to send INSTREAM command: %w", err)
	}

	// Read and send file in chunks
	buffer := make([]byte, 4096)
	for {
		select {
		case <-ctx.Done():
			return nil, ErrTimeout
		default:
			n, err := reader.Read(buffer)
			if err == io.EOF {
				break
			}
			if err != nil {
				return nil, fmt.Errorf("failed to read file: %w", err)
			}

			// Send chunk size
			if _, err := fmt.Fprintf(c.conn, "%d", n); err != nil {
				return nil, fmt.Errorf("failed to send chunk size: %w", err)
			}

			// Send chunk data
			if _, err := c.conn.Write(buffer[:n]); err != nil {
				return nil, fmt.Errorf("failed to send chunk data: %w", err)
			}
		}
	}

	// Send end of file marker
	if _, err := fmt.Fprintf(c.conn, "0"); err != nil {
		return nil, fmt.Errorf("failed to send EOF marker: %w", err)
	}

	// Read response
	response := make([]byte, 1024)
	n, err := c.conn.Read(response)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	result := string(response[:n])
	if result == "stream: OK\n" {
		return &ScanResult{Infected: false}, nil
	}

	// Parse virus name from response
	virus := ""
	if len(result) > 8 {
		virus = result[8 : len(result)-1]
	}

	return &ScanResult{
		Infected: true,
		Virus:    virus,
	}, nil
}

func (c *ClamAVClient) Close() error {
	return c.conn.Close()
}
