package antivirus

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

type QuarantineMetadata struct {
	OriginalPath   string    `json:"original_path"`
	QuarantineTime time.Time `json:"quarantine_time"`
	VirusName      string    `json:"virus_name"`
	FileSize       int64     `json:"file_size"`
}

type QuarantineManager struct {
	quarantineDir string
	retention     time.Duration
}

func NewQuarantineManager(dir string, retention time.Duration) (*QuarantineManager, error) {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create quarantine directory: %w", err)
	}
	return &QuarantineManager{
		quarantineDir: dir,
		retention:     retention,
	}, nil
}

func (q *QuarantineManager) QuarantineFile(filePath string, virusName string) error {
	// Get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Generate quarantine path
	quarantinePath := filepath.Join(q.quarantineDir, fmt.Sprintf("%d_%s", time.Now().UnixNano(), filepath.Base(filePath)))

	// Create metadata
	metadata := QuarantineMetadata{
		OriginalPath:   filePath,
		QuarantineTime: time.Now(),
		VirusName:      virusName,
		FileSize:       fileInfo.Size(),
	}

	// Write metadata
	metadataPath := quarantinePath + ".meta"
	metadataFile, err := os.Create(metadataPath)
	if err != nil {
		return fmt.Errorf("failed to create metadata file: %w", err)
	}
	defer metadataFile.Close()

	if err := json.NewEncoder(metadataFile).Encode(metadata); err != nil {
		return fmt.Errorf("failed to write metadata: %w", err)
	}

	// Move file to quarantine
	if err := os.Rename(filePath, quarantinePath); err != nil {
		return fmt.Errorf("failed to move file to quarantine: %w", err)
	}

	return nil
}

func (q *QuarantineManager) RestoreFile(quarantinePath string) error {
	// Read metadata
	metadataPath := quarantinePath + ".meta"
	metadataFile, err := os.Open(metadataPath)
	if err != nil {
		return fmt.Errorf("failed to open metadata file: %w", err)
	}
	defer metadataFile.Close()

	var metadata QuarantineMetadata
	if err := json.NewDecoder(metadataFile).Decode(&metadata); err != nil {
		return fmt.Errorf("failed to read metadata: %w", err)
	}

	// Restore file
	if err := os.Rename(quarantinePath, metadata.OriginalPath); err != nil {
		return fmt.Errorf("failed to restore file: %w", err)
	}

	// Remove metadata file
	if err := os.Remove(metadataPath); err != nil {
		return fmt.Errorf("failed to remove metadata file: %w", err)
	}

	return nil
}

func (q *QuarantineManager) Cleanup() error {
	cutoff := time.Now().Add(-q.retention)

	entries, err := os.ReadDir(q.quarantineDir)
	if err != nil {
		return fmt.Errorf("failed to read quarantine directory: %w", err)
	}

	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}

		if filepath.Ext(entry.Name()) == ".meta" {
			metadataPath := filepath.Join(q.quarantineDir, entry.Name())
			metadataFile, err := os.Open(metadataPath)
			if err != nil {
				continue
			}

			var metadata QuarantineMetadata
			if err := json.NewDecoder(metadataFile).Decode(&metadata); err != nil {
				metadataFile.Close()
				continue
			}
			metadataFile.Close()

			if metadata.QuarantineTime.Before(cutoff) {
				// Remove quarantined file
				quarantinePath := metadataPath[:len(metadataPath)-5] // Remove .meta extension
				os.Remove(quarantinePath)
				os.Remove(metadataPath)
			}
		}
	}

	return nil
}
