version: '3'
services:
  clamav:
    image: clamav/clamav:latest
    container_name: clamav
    restart: unless-stopped
    volumes:
      - clamav_data:/var/lib/clamav
      - quarantine:/quarantine
    ports:
      - "3310:3310"
    environment:
      - CLAMAV_NO_FRESHCLAM=false
      - CLAMAV_NO_MILTERD=true
      - CLAMAV_NO_PUA=true
      - CLAMAV_NO_LOG=false
    healthcheck:
      test: ["CMD", "clamdscan", "--version"]
      interval: 30s
      timeout: 10s
      retries: 5
    command: ["/bin/sh", "-c", "freshclam && crond -f & clamd"]

  liveutil:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: liveutil
    restart: unless-stopped
    depends_on:
      clamav:
        condition: service_healthy
    environment:
      - ANTIVIRUS_CLAMAV_ADDR=clamav:3310
      - ANTIVIRUS_QUARANTINE=/quarantine
    volumes:
      - quarantine:/quarantine
    ports:
      - "8080:80" # map host 8080 to container 80

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: frontend
    restart: unless-stopped
    ports:
      - "3000:80" # map host 3000 to container 80
    depends_on:
      - liveutil

volumes:
  clamav_data:
  quarantine: 