package fileutils

import "testing"

func TestCommonPrefix(t *testing.T) {
	testCases := map[string]struct {
		paths []string
		want  string
	}{
		"same lvl": {
			paths: []string{
				"/home/<USER>/file1",
				"/home/<USER>/file2",
			},
			want: "/home/<USER>",
		},
		"sub folder": {
			paths: []string{
				"/home/<USER>/folder",
				"/home/<USER>/folder/file",
			},
			want: "/home/<USER>/folder",
		},
		"relative path": {
			paths: []string{
				"/home/<USER>/folder",
				"/home/<USER>/folder/../folder2",
			},
			want: "/home/<USER>",
		},
		"no common path": {
			paths: []string{
				"/home/<USER>/folder",
				"/etc/file",
			},
			want: "",
		},
	}
	for name, tt := range testCases {
		t.Run(name, func(t *testing.T) {
			if got := CommonPrefix('/', tt.paths...); got != tt.want {
				t.Errorf("CommonPrefix() = %v, want %v", got, tt.want)
			}
		})
	}
}
