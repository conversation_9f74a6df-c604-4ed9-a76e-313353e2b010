{"name": "filebrowser-frontend", "version": "3.0.0", "private": true, "type": "module", "engines": {"node": ">=22.0.0", "pnpm": ">=9.0.0"}, "scripts": {"dev": "vite dev", "build": "pnpm run typecheck && vite build", "clean": "find ./dist -maxdepth 1 -mindepth 1 ! -name '.gitkeep' -exec rm -r {} +", "typecheck": "vue-tsc -p ./tsconfig.tsc.json --noEmit", "lint": "eslint src/", "lint:fix": "eslint --fix src/", "format": "prettier --write .", "test": "playwright test"}, "dependencies": {"@chenfengyuan/vue-number-input": "^2.0.1", "@vueuse/core": "^12.5.0", "@vueuse/integrations": "^12.5.0", "ace-builds": "^1.37.5", "core-js": "^3.40.0", "dayjs": "^1.11.10", "epubjs": "^0.3.93", "filesize": "^10.1.1", "js-base64": "^3.7.7", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "marked": "^15.0.6", "material-icons": "^1.13.13", "normalize.css": "^8.0.1", "pinia": "^2.3.1", "pretty-bytes": "^6.1.1", "qrcode.vue": "^3.4.1", "tus-js-client": "^4.3.1", "utif": "^3.1.0", "video.js": "^8.21.0", "videojs-hotkeys": "^0.2.28", "videojs-mobile-ui": "^1.1.1", "vue": "^3.4.21", "vue-final-modal": "^4.5.4", "vue-i18n": "^11.1.2", "vue-lazyload": "^3.0.0", "vue-reader": "^1.2.17", "vue-router": "^4.3.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.3", "@playwright/test": "^1.50.0", "@tsconfig/node22": "^22.0.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.10", "@typescript-eslint/eslint-plugin": "^8.21.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.19", "concurrently": "^9.1.2", "eslint": "^9.19.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.24.0", "jsdom": "^26.0.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "terser": "^5.37.0", "vite": "^6.1.6", "vite-plugin-compression2": "^1.0.0", "vue-tsc": "^2.2.0"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}