body {
  font-family: "Roboto", sans-serif;
  padding-top: 4em;
  background: var(--background);
  color: var(--textSecondary);
}

* {
  box-sizing: border-box;
}

*,
*:hover,
*:active,
*:focus {
  outline: 0;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
}

audio,
video {
  width: 100%;
}

.mobile-only {
  display: none !important;
}

.container {
  width: 95%;
  max-width: 960px;
  margin: 1em auto 0;
}

i.spin {
  animation: 1s spin linear infinite;
}

#app {
  transition: 0.2s ease padding;
}

#app.multiple {
  padding-bottom: 4em;
}

nav {
  width: 16em;
  position: fixed;
  top: 4em;
  left: 0;
}

html[dir="rtl"] nav {
  left: initial;
  right: 0;
}

nav .action {
  width: 100%;
  display: block;
  border-radius: 0;
  font-size: 1.1em;
  padding: 0.5em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

html[dir="rtl"] nav .action {
  text-align: right;
}

nav > div {
  border-top: 1px solid var(--divider);
}

nav .action > * {
  vertical-align: middle;
}

main {
  min-height: 1em;
  margin: 0 1em 1em auto;
  width: calc(100% - 19em);
}

.breadcrumbs {
  height: 3em;
  background: var(--background);
  border-bottom: 1px solid var(--divider);
}

.breadcrumbs span,
.breadcrumbs {
  display: flex;
  align-items: center;
  color: var(--textPrimary);
}

.breadcrumbs a {
  color: inherit;
  transition: 0.1s ease-in;
  border-radius: 0.125em;
}

html[dir="rtl"] .breadcrumbs a {
  transform: translateX(-16em);
}

.breadcrumbs a:hover {
  background-color: var(--divider);
}

.breadcrumbs span a {
  padding: 0.2em;
}

.files {
  position: absolute;
  bottom: 30px;
  width: 100%;
}

.progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  z-index: 9999999999;
}

.progress div {
  height: 100%;
  background-color: #40c4ff;
  width: 0;
  transition: 0.2s ease width;
}

.break-word {
  word-break: break-all;
}

.vue-number-input > input {
  background: var(--surfacePrimary) !important;
  border-color: var(--surfaceSecondary) !important;
  color: var(--textSecondary) !important;
}

.vue-number-input--small > input {
  height: 1rem !important;
  font-size: 1rem !important;
}

.vue-number-input :hover,
.vue-number-input :focus {
  border-color: var(--borderSecondary) !important;
}

.vue-number-input__button {
  background: var(--surfacePrimary) !important;
}

.vue-number-input__button--minus,
.vue-number-input__button--plus {
  border-color: var(--surfaceSecondary) !important;
}

.vue-number-input__button::before,
.vue-number-input__button::after {
  background: var(--textSecondary) !important;
}

.vfm-modal {
  z-index: 9999999 !important;
}
