@import "material-icons/iconfont/filled.css";

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-cyrillic-ext.woff2) format("woff2");
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-cyrillic.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-greek-ext.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-greek.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-vietnamese.woff2) format("woff2");
  unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-latin-ext.woff2) format("woff2");
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF,
    U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  src:
    local("Roboto"),
    local("Roboto-Regular"),
    url(../assets/fonts/roboto/normal-latin.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-cyrillic-ext.woff2) format("woff2");
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-cyrillic.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-greek-ext.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-greek.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-vietnamese.woff2) format("woff2");
  unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-latin-ext.woff2) format("woff2");
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF,
    U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  src:
    local("Roboto Medium"),
    local("Roboto-Medium"),
    url(../assets/fonts/roboto/medium-latin.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-cyrillic-ext.woff2) format("woff2");
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-cyrillic.woff2) format("woff2");
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-greek-ext.woff2) format("woff2");
  unicode-range: U+1F00-1FFF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-greek.woff2) format("woff2");
  unicode-range: U+0370-03FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-vietnamese.woff2) format("woff2");
  unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-latin-ext.woff2) format("woff2");
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF,
    U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  src:
    local("Roboto Bold"),
    local("Roboto-Bold"),
    url(../assets/fonts/roboto/bold-latin.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

.material-icons {
  font-size: 1.5rem;
}
