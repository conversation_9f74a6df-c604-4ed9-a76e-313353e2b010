{"buttons": {"cancel": "Cancel", "clear": "Clear", "close": "Close", "continue": "Continue", "copy": "Copy", "copyFile": "Copy file", "copyToClipboard": "Copy to clipboard", "copyDownloadLinkToClipboard": "Copy download link to clipboard", "create": "Create", "delete": "Delete", "download": "Download", "file": "File", "folder": "Folder", "fullScreen": "Toggle full screen", "hideDotfiles": "Hide dotfiles", "info": "Info", "more": "More", "move": "Move", "moveFile": "Move file", "new": "New", "next": "Next", "ok": "OK", "permalink": "Get Permanent Link", "previous": "Previous", "preview": "Preview", "publish": "Publish", "rename": "<PERSON><PERSON>", "replace": "Replace", "reportIssue": "Report Issue", "save": "Save", "schedule": "Schedule", "search": "Search", "select": "Select", "selectMultiple": "Select multiple", "share": "Share", "shell": "Toggle shell", "submit": "Submit", "switchView": "Switch view", "toggleSidebar": "Toggle sidebar", "update": "Update", "upload": "Upload", "openFile": "Open file", "discardChanges": "Discard"}, "download": {"downloadFile": "Download File", "downloadFolder": "Download Folder", "downloadSelected": "Download Selected"}, "upload": {"abortUpload": "Are you sure you wish to abort?", "scanning": "Scanning for viruses..."}, "errors": {"forbidden": "You don't have permissions to access this.", "internal": "Something really went wrong.", "notFound": "This location can't be reached.", "connection": "The server can't be reached."}, "files": {"body": "Body", "closePreview": "Close preview", "files": "Files", "folders": "Folders", "home": "Home", "lastModified": "Last modified", "loading": "Loading...", "lonely": "It feels lonely here...", "metadata": "<PERSON><PERSON><PERSON>", "multipleSelectionEnabled": "Multiple selection enabled", "name": "Name", "size": "Size", "sortByLastModified": "Sort by last modified", "sortByName": "Sort by name", "sortBySize": "Sort by size", "noPreview": "Preview is not available for this file."}, "help": {"click": "select file or directory", "ctrl": {"click": "select multiple files or directories", "f": "opens search", "s": "save a file or download the directory where you are"}, "del": "delete selected items", "doubleClick": "open a file or directory", "esc": "clear selection and/or close the prompt", "f1": "this information", "f2": "rename file", "help": "Help"}, "login": {"createAnAccount": "Create an account", "loginInstead": "Already have an account", "password": "Password", "passwordConfirm": "Password Confirmation", "passwordsDontMatch": "Passwords don't match", "signup": "Signup", "submit": "<PERSON><PERSON>", "username": "Username", "usernameTaken": "Username already taken", "wrongCredentials": "Wrong credentials"}, "permanent": "Permanent", "prompts": {"copy": "Copy", "copyMessage": "Choose the location to copy your files to:", "currentlyNavigating": "Currently navigating on:", "deleteMessageMultiple": "Are you sure you wish to delete {count} file(s)?", "deleteMessageSingle": "Are you sure you wish to delete this file/folder?", "deleteMessageShare": "Are you sure you wish to delete this share({path})?", "deleteUser": "Are you sure you want to delete this user?", "deleteTitle": "Delete files", "displayName": "Display Name:", "download": "Download files", "downloadMessage": "Choose the format you wish to download.", "error": "Something went wrong", "fileInfo": "File information", "filesSelected": "{count} files selected.", "lastModified": "Last Modified", "move": "Move", "moveMessage": "Choose new home for your file(s)/folder(s):", "newArchetype": "Create a new post based on an archetype. Your file will be created on content folder.", "newDir": "New directory", "newDirMessage": "Name your new directory.", "newFile": "New file", "newFileMessage": "Name your new file.", "numberDirs": "Number of directories", "numberFiles": "Number of files", "rename": "<PERSON><PERSON>", "renameMessage": "Insert a new name for", "replace": "Replace", "replaceMessage": "One of the files you're trying to upload has a conflicting name. Do you wish to skip this file and continue to upload or replace the existing one?\n", "schedule": "Schedule", "scheduleMessage": "Pick a date and time to schedule the publication of this post.", "show": "Show", "size": "Size", "upload": "Upload", "uploadFiles": "Uploading {files} files...", "uploadMessage": "Select an option to upload.", "optionalPassword": "Optional password", "resolution": "Resolution", "discardEditorChanges": "Are you sure you wish to discard the changes you've made?"}, "search": {"images": "Images", "music": "Music", "pdf": "PDF", "pressToSearch": "Press enter to search...", "search": "Search...", "typeToSearch": "Type to search...", "types": "Types", "video": "Video"}, "settings": {"admin": "Admin", "administrator": "Administrator", "allowCommands": "Execute commands", "allowEdit": "Edit, rename and delete files or directories", "allowNew": "Create new files and directories", "allowPublish": "Publish new posts and pages", "allowSignup": "Allow users to signup", "avoidChanges": "(leave blank to avoid changes)", "branding": "Branding", "brandingDirectoryPath": "Branding directory path", "brandingHelp": "You can customize how your File Browser instance looks and feels by changing its name, replacing the logo, adding custom styles and even disable external links to GitHub.\nFor more information about custom branding, please check out the {0}.", "changePassword": "Change Password", "commandRunner": "Command runner", "commandRunnerHelp": "Here you can set commands that are executed in the named events. You must write one per line. The environment variables {0} and {1} will be available, being {0} relative to {1}. For more information about this feature and the available environment variables, please read the {2}.", "commandsUpdated": "Commands updated!", "createUserDir": "Auto create user home dir while adding new user", "tusUploads": "Chunked Uploads", "tusUploadsHelp": "File Browser supports chunked file uploads, allowing for the creation of efficient, reliable, resumable and chunked file uploads even on unreliable networks.", "tusUploadsChunkSize": "Indicates to maximum size of a request (direct uploads will be used for smaller uploads). You may input a plain integer denoting byte size input or a string like 10MB, 1GB etc.", "tusUploadsRetryCount": "Number of retries to perform if a chunk fails to upload.", "userHomeBasePath": "Base path for user home directories", "userScopeGenerationPlaceholder": "The scope will be auto generated", "createUserHomeDirectory": "Create user home directory", "customStylesheet": "Custom Stylesheet", "defaultUserDescription": "These are the default settings for new users.", "disableExternalLinks": "Disable external links (except documentation)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentation", "examples": "Examples", "executeOnShell": "Execute on shell", "executeOnShellDescription": "By default, File Browser executes the commands by calling their binaries directly. If you wish to run them on a shell instead (such as Bash or PowerShell), you can define it here with the required arguments and flags. If set, the command you execute will be appended as an argument. This applies to both user commands and event hooks.", "globalRules": "This is a global set of allow and disallow rules. They apply to every user. You can define specific rules on each user's settings to override these ones.", "globalSettings": "Global Settings", "hideDotfiles": "Hide dotfiles", "insertPath": "Insert the path", "insertRegex": "Insert regex expression", "instanceName": "Instance name", "language": "Language", "lockPassword": "Prevent the user from changing the password", "newPassword": "Your new password", "newPasswordConfirm": "Confirm your new password", "newUser": "New User", "password": "Password", "passwordUpdated": "Password updated!", "path": "Path", "perm": {"create": "Create files and directories", "delete": "Delete files and directories", "download": "Download", "execute": "Execute commands", "modify": "Edit files", "rename": "Rename or move files and directories", "share": "Share files"}, "permissions": "Permissions", "permissionsHelp": "You can set the user to be an administrator or choose the permissions individually. If you select \"Administrator\", all of the other options will be automatically checked. The management of users remains a privilege of an administrator.\n", "profileSettings": "Profile Settings", "ruleExample1": "prevents the access to any dotfile (such as .git, .gitignore) in every folder.\n", "ruleExample2": "blocks the access to the file named Caddyfile on the root of the scope.", "rules": "Rules", "rulesHelp": "Here you can define a set of allow and disallow rules for this specific user. The blocked files won't show up in the listings and they wont be accessible to the user. We support regex and paths relative to the users scope.\n", "scope": "<PERSON><PERSON>", "setDateFormat": "Set exact date format", "settingsUpdated": "Settings updated!", "shareDuration": "Share Duration", "shareManagement": "Share Management", "shareDeleted": "Share deleted!", "singleClick": "Use single clicks to open files and directories", "themes": {"default": "System default", "dark": "Dark", "light": "Light", "title": "Theme"}, "user": "User", "userCommands": "Commands", "userCommandsHelp": "A space separated list with the available commands for this user. Example:\n", "userCreated": "User created!", "userDefaults": "User default settings", "userDeleted": "User deleted!", "userManagement": "User Management", "userUpdated": "User updated!", "username": "Username", "users": "Users"}, "sidebar": {"help": "Help", "hugoNew": "<PERSON>", "login": "<PERSON><PERSON>", "logout": "Logout", "myFiles": "My files", "newFile": "New file", "newFolder": "New folder", "preview": "Preview", "settings": "Settings", "signup": "Signup", "siteSettings": "Site Settings"}, "success": {"linkCopied": "Link copied!"}, "time": {"days": "Days", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "unit": "Time Unit"}}