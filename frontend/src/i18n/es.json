{"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "clear": "Limpiar", "close": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copyFile": "Copiar archivo", "copyToClipboard": "Copiar al portapapeles", "create": "<PERSON><PERSON><PERSON>", "delete": "Bo<PERSON>r", "download": "<PERSON><PERSON><PERSON>", "file": "Archivo", "folder": "Carpeta", "hideDotfiles": "Ocultar archivos empezados por punto", "info": "Info", "more": "Más", "move": "Mover", "moveFile": "Mover archivo", "new": "Nuevo", "next": "Siguient<PERSON>", "ok": "OK", "permalink": "<PERSON>e", "previous": "Anterior", "publish": "Publicar", "rename": "Renombrar", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reportIssue": "Reportar problema", "save": "Guardar", "schedule": "Programar", "search": "Buscar", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectMultiple": "Sele<PERSON><PERSON> múl<PERSON>", "share": "Compartir", "shell": "Presiona Enter para buscar...", "submit": "Enviar", "switchView": "Cambiar vista", "toggleSidebar": "Mostrar/Ocultar menú", "update": "Actualizar", "upload": "Subir", "openFile": "Abrir archivo"}, "download": {"downloadFile": "<PERSON><PERSON><PERSON>o", "downloadFolder": "Descargar directorio", "downloadSelected": "<PERSON><PERSON><PERSON>"}, "errors": {"forbidden": "No tienes los permisos necesarios para acceder.", "internal": "La verdad es que algo ha ido mal.", "notFound": "No se puede acceder a este lugar.", "connection": "No se puede acceder al servidor."}, "files": {"body": "<PERSON><PERSON><PERSON>", "closePreview": "Cerrar vista previa", "files": "Archivos", "folders": "Carpetas", "home": "<PERSON><PERSON>o", "lastModified": "Última modificación", "loading": "Cargando...", "lonely": "Uno se siente muy sólo aquí...", "metadata": "Metadatos", "multipleSelectionEnabled": "Selección múltiple activada", "name": "Nombre", "size": "<PERSON><PERSON><PERSON>", "sortByLastModified": "Ordenar por última modificación", "sortByName": "Ordenar por nombre", "sortBySize": "Ordenar por tamaño", "noPreview": "La vista previa no está disponible para este archivo."}, "help": {"click": "seleccionar archivo o carpeta", "ctrl": {"click": "seleccionar múl<PERSON>les archivos o carpetas", "f": "abre la búsqueda", "s": "guarda un archivo o lo descarga a la carpeta en la que estás"}, "del": "elimina los items seleccionados", "doubleClick": "abre un archivo o carpeta", "esc": "limpia la selección y/o cierra la ventana", "f1": "esta información", "f2": "renombrar archivo", "help": "<PERSON><PERSON><PERSON>"}, "login": {"createAnAccount": "<PERSON><PERSON><PERSON> una cuenta", "loginInstead": "Usuario ya existente", "password": "Contraseña", "passwordConfirm": "Confirmación de contraseña", "passwordsDontMatch": "Las contraseñas no coinciden", "signup": "Registrate", "submit": "In<PERSON><PERSON>", "username": "Usuario", "usernameTaken": "Nombre usuario no disponible", "wrongCredentials": "Usuario y/o contraseña incorrectos"}, "permanent": "Permanente", "prompts": {"copy": "Copiar", "copyMessage": "Elige el lugar donde quieres copiar tus archivos:", "currentlyNavigating": "Actualmente estás en:", "deleteMessageMultiple": "¿Estás seguro que quieres eliminar {count} archivo(s)?", "deleteMessageSingle": "¿Estás seguro que quieres eliminar este archivo/carpeta?", "deleteMessageShare": "¿Está seguro de que quiere eliminar este recurso compartido({path})?", "deleteTitle": "Borrar archivos", "displayName": "Nombre:", "download": "Descargar archivos", "downloadMessage": "Elige el formato de descarga.", "error": "Algo ha fallado", "fileInfo": "Información del archivo", "filesSelected": "{count} archivos seleccionados.", "lastModified": "Última modificación", "move": "Mover", "moveMessage": "Elige una nueva casa para tus archivo(s)/carpeta(s):", "newArchetype": "Crea un nuevo post basado en un arquetipo. Tu archivo será creado en la carpeta de contenido.", "newDir": "Nueva carpeta", "newDirMessage": "Escribe el nombre de la nueva carpeta.", "newFile": "Nuevo archivo", "newFileMessage": "Escribe el nombre del nuevo archivo.", "numberDirs": "Número de <PERSON>", "numberFiles": "Número de archivos", "rename": "Renombrar", "renameMessage": "Escribe el nuevo nombre para", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "replaceMessage": "Uno de los archivos ue intentas subir está creando conflicto por su nombre. ¿Quieres cambiar el nombre del ya existente?\n", "schedule": "Programar", "scheduleMessage": "Elige una hora y fecha para programar la publicación de este post.", "show": "Mostrar", "size": "<PERSON><PERSON><PERSON>", "upload": "Subir", "uploadFiles": "Subiendo {files} archivos...", "uploadMessage": "Seleccione una opción para subir.", "optionalPassword": "Contraseña opcional"}, "search": {"images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "music": "Música", "pdf": "PDF", "pressToSearch": "<PERSON><PERSON><PERSON> enter para buscar...", "search": "Buscar...", "typeToSearch": "Escribe para realizar una busqueda...", "types": "Tipos", "video": "Vídeo"}, "settings": {"admin": "Admin", "administrator": "Administrador", "allowCommands": "Ejecutar comandos", "allowEdit": "Editar, renombrar y borrar archivos o carpetas", "allowNew": "Crear nuevos archivos y carpetas", "allowPublish": "Publicar nuevos posts y páginas", "allowSignup": "Permitir registro de usuarios", "avoidChanges": "(dejar en blanco para evitar cambios)", "branding": "<PERSON><PERSON>", "brandingDirectoryPath": "Ruta de la carpeta de personalizacion de marca", "brandingHelp": "Tú puedes personalizar como se ve tu instancia de FileBrowser cambiándole el nombre, reemplazando el<PERSON>, agregar estilos personalizados e incluso deshabilitando loslinks externos que apuntan hacia GitHub. \nPara mayor información acerca de personalización de marca, por favor revisa el {0}.", "changePassword": "Cambiar contraseña", "commandRunner": "Executor de comandos", "commandRunnerHelp": "Aquí puede establecer los comandos que se ejecutan en los eventos nombrados. Debe escribir uno por línea. Las variables de entorno {0} y {1} estarán disponibles, siendo {0} relativa a {1}. Para más información sobre esta característica y las variables de entorno disponibles, por favor lea el {2}.", "commandsUpdated": "¡Comandos actualizados!", "createUserDir": "Crea automaticamente una carpeta de inicio cuando se agrega un usuario", "userHomeBasePath": "Ruta base para los directorios personales de los usuarios", "userScopeGenerationPlaceholder": "El ámbito se generará automáticamente", "createUserHomeDirectory": "Crear el directorio principal del usuario", "customStylesheet": "Modificar hoja de estilos", "defaultUserDescription": "<PERSON><PERSON>s son las configuraciones por defecto para nuevos usuarios.", "disableExternalLinks": "Deshabilitar enlaces externos (excepto documentación)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentación", "examples": "<PERSON><PERSON><PERSON><PERSON>", "executeOnShell": "Ejecutar en la shell", "executeOnShellDescription": "<PERSON>r defecto, FileBrowser ejecuta los comandos llamando directamente a sus binarios. Si quieres ejecutarlos en un shell en su lugar (como Bash o PowerShell), puedes definirlo aquí con los argumentos y banderas (flags) necesarios. Si se define, el comando que se ejecuta se añadirá como argumento. Esto se aplica tanto a los comandos de usuario como a los ganchos de eventos.", "globalRules": "Se trata de un conjunto global de reglas de permiso y rechazo. Se aplican a todos los usuarios. Puedes definir reglas específicas en la configuración de cada usuario para anular estas.", "globalSettings": "Ajustes globales", "hideDotfiles": "Ocultar archivos empezados por punto", "insertPath": "Introduce la ruta", "insertRegex": "Introducir expresión regular", "instanceName": "Nombre de la instancia", "language": "Idioma", "lockPassword": "Evitar que el usuario cambie la contraseña", "newPassword": "Tu nueva contraseña", "newPasswordConfirm": "Confirma tu contraseña", "newUser": "Nuevo usuario", "password": "Contraseña", "passwordUpdated": "¡Contraseña actualizada!", "path": "<PERSON><PERSON>", "perm": {"create": "<PERSON><PERSON>r ficheros y directorios", "delete": "Eliminar ficheros y directorios", "download": "<PERSON><PERSON><PERSON>", "execute": "Executar comandos", "modify": "<PERSON><PERSON>", "rename": "Renombrar o mover ficheros y directorios", "share": "Compartir ficheros"}, "permissions": "<PERSON><PERSON><PERSON>", "permissionsHelp": "Puedes nombrar al usuario como administrador o elegir los permisos individualmente. Si seleccionas \"Administrador\", todas las otras opciones serán activadas automáticamente. La administración de usuarios es un privilegio de administrador.\n", "profileSettings": "Ajustes del perfil", "ruleExample1": "previene el acceso a una extensión de archivo (Como .git) en cada carpeta.\n", "ruleExample2": "bloquea el acceso al archivo llamado Caddyfile en la carpeta raíz.", "rules": "Reg<PERSON>", "rulesHelp": "Aquí puedes definir un conjunto de reglas de permisos para este usuario específico. Los archivos bloqueados no se mostrarán en las listas y no serán accesibles por el usuario. Puedes utilizar regex y rutas relativas a la raíz del usuario.\n", "scope": "<PERSON><PERSON><PERSON>", "setDateFormat": "Establecer el formato exacto de la fecha", "settingsUpdated": "¡Ajustes actualizados!", "shareDuration": "Compartir Duración", "shareManagement": "Gestión Compartida", "shareDeleted": "¡Recurso compartido eliminado!", "singleClick": "Utilice un solo clic para abrir archivos y directorios", "themes": {"dark": "Oscuro", "light": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "user": "Usuario", "userCommands": "<PERSON><PERSON><PERSON>", "userCommandsHelp": "Una lista separada por espacios con los comandos permitidos para este usuario. Ejemplo:\n", "userCreated": "¡Usuario creado!", "userDefaults": "Configuración de usuario por defecto", "userDeleted": "¡Usuario eliminado!", "userManagement": "Administración de usuarios", "userUpdated": "¡Usuario actualizado!", "username": "Usuario", "users": "Usuarios"}, "sidebar": {"help": "<PERSON><PERSON><PERSON>", "hugoNew": "Nuevo Hugo", "login": "In<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "myFiles": "Mis archivos", "newFile": "Nuevo archivo", "newFolder": "Nueva carpeta", "preview": "Vista previa", "settings": "<PERSON><PERSON><PERSON><PERSON>", "signup": "Registrate", "siteSettings": "Ajustes del sitio"}, "success": {"linkCopied": "¡Link copiado!"}, "time": {"days": "Días", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "unit": "Unidad"}}