{"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "clear": "Can<PERSON><PERSON>", "close": "<PERSON><PERSON>", "continue": "Continua", "copy": "Copia", "copyFile": "Copia file", "copyToClipboard": "Copia negli appunti", "create": "<PERSON><PERSON>", "delete": "Elimina", "download": "Scarica", "hideDotfiles": "Nascondi dotfile", "info": "Informazioni", "more": "Altro", "move": "Sposta", "moveFile": "Sposta file", "new": "Nuovo", "next": "Successivo", "ok": "OK", "permalink": "Ottieni link permanente", "previous": "Precedente", "publish": "Publica", "rename": "Rinomina", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportIssue": "Se<PERSON>la un problema", "save": "<PERSON><PERSON>", "schedule": "Programma", "search": "Cerca", "select": "Seleziona", "selectMultiple": "Seleziona m<PERSON>ep<PERSON>i", "share": "Condi<PERSON><PERSON>", "shell": "Mostra/nascondi shell", "switchView": "Cambia vista", "toggleSidebar": "Mostra/nascondi la barra laterale", "update": "Aggiorna", "upload": "Carica"}, "download": {"downloadFile": "Scarica file", "downloadFolder": "Sc<PERSON>ca cartella", "downloadSelected": "Scarica selezionati"}, "errors": {"forbidden": "Non hai i permessi per accedere a questo file.", "internal": "Qualcosa è andato veramente male.", "notFound": "<PERSON>o percorso non può essere raggiunto."}, "files": {"body": "<PERSON><PERSON><PERSON>", "closePreview": "<PERSON><PERSON> antep<PERSON>", "files": "File", "folders": "<PERSON><PERSON><PERSON>", "home": "Home", "lastModified": "Ultima modifica", "loading": "Caricamento...", "lonely": "Ci si sente soli qui...", "metadata": "<PERSON><PERSON><PERSON>", "multipleSelectionEnabled": "Selezione multipla attivata", "name": "Nome", "size": "Dimensione", "sortByLastModified": "Ordina per ultima modifica", "sortByName": "Ordina per nome", "sortBySize": "Ordina per dimensione"}, "help": {"click": "seleziona un file o una cartella", "ctrl": {"click": "seleziona più file o cartelle", "f": "apre la barra di ricerca", "s": "salva un file o scarica la cartella in cui ci si trova"}, "del": "elimina gli elementi selezionati", "doubleClick": "apre un file o una cartella", "esc": "annulla la selezione e/o chiude la finestra aperta", "f1": "questo pannello", "f2": "rinomina un file", "help": "<PERSON><PERSON>"}, "login": {"createAnAccount": "Crea un account", "loginInstead": "Hai già un account", "password": "Password", "passwordConfirm": "Conferma password", "passwordsDontMatch": "Le password non corrispondono", "signup": "Registrati", "submit": "Entra", "username": "Nome utente", "usernameTaken": "Username gi<PERSON> usato", "wrongCredentials": "Credenziali errate"}, "permanent": "Permanente", "prompts": {"copy": "Copia", "copyMessage": "Seleziona la cartella in cui copiare i file:", "currentlyNavigating": "Attualmente navigando su:", "deleteMessageMultiple": "Sei sicuro di voler eliminare {count} file?", "deleteMessageSingle": "Sei sicuro di voler eliminare questo file/cartella?", "deleteTitle": "Elimina", "displayName": "Nome visualizzato:", "download": "Scarica files", "downloadMessage": "Seleziona il formato che vuoi scaricare.", "error": "Qualcosa è andato per il verso storto", "fileInfo": "Informazioni sul file", "filesSelected": "{count} file selezionati.", "lastModified": "Ultima modifica", "move": "Sposta", "moveMessage": "Seleziona la nuova posizione per i tuoi file e/o cartella/e:", "newArchetype": "Crea un nuovo post basato su un modello. Il tuo file verrà creato nella cartella.", "newDir": "Nuova cartella", "newDirMessage": "Scrivi il nome della nuova cartella.", "newFile": "Nuovo file", "newFileMessage": "Scrivi il nome del nuovo file.", "numberDirs": "Numero di cartelle", "numberFiles": "Numero di file", "rename": "Rinomina", "renameMessage": "Inserisci un nuovo nome per", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replaceMessage": "Uno dei file che stai cercando di caricare sta generando un conflitto per via del suo nome. Desideri sostituire il file già esistente?\n", "schedule": "Pianifica", "scheduleMessage": "Seleziona data e ora per programmare la pubbilicazione di questo post", "show": "Mostra", "size": "Dimensione", "upload": "Carica", "uploadMessage": "Seleziona un'opzione per il caricamento."}, "search": {"images": "<PERSON><PERSON><PERSON><PERSON>", "music": "Musica", "pdf": "PDF", "pressToSearch": "Premi Invio per cercare...", "search": "Cerca...", "typeToSearch": "Scrivi per cercare...", "types": "T<PERSON><PERSON>", "video": "Video"}, "settings": {"admin": "Admin", "administrator": "Amministratore", "allowCommands": "<PERSON><PERSON><PERSON><PERSON> comandi", "allowEdit": "Modifica, rinomina ed elimina file o cartelle", "allowNew": "Crea nuovi files o cartelle", "allowPublish": "Pubblica nuovi post e pagine", "allowSignup": "Permetti agli utenti di registrarsi", "avoidChanges": "(lascia vuoto per evitare cambiamenti)", "branding": "Branding", "brandingDirectoryPath": "Directory del branding", "brandingHelp": "Puoi personalizzare l'aspetto e il comportamento di File Browser cambiando il suo nome, logo, aggiungendo stili personalizzati e anche disabilitando link esterni verso GitHub.\nPer altre informazioni sul branding personalizzato, vai alla {0}.", "changePassword": "Modifica password", "commandRunner": "Esecutore di comandi", "commandRunnerHelp": "Qui puoi impostare i comandi da eseguire negli eventi nominati. Ne devi scrivere uno per riga. Le variabili d'ambiente {0} e {1} sono disponibili, essendo {0} relativo a {1}. Per altre informazioni su questa funzionalità e sulle variabili d'ambiente utilizzabili, leggi la {2}.", "commandsUpdated": "Comandi aggiornati!", "createUserDir": "Crea automaticamente la home directory dell'utente quando lo aggiungi", "customStylesheet": "Foglio di stile personaliz<PERSON>to", "defaultUserDescription": "Queste sono le impostazioni predefinite per i nuovi utenti.", "disableExternalLinks": "Disabilita link esterni (tranne per la documentazione)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentazione", "examples": "Esempi", "executeOnShell": "Esegui nella shell", "executeOnShellDescription": "Di default File Browser esegue i comandi chiamando direttamente i loro binari. Se invece vuoi eseguirli su una shell (come Bash o PowerShell), puoi definirli qui con gli argomenti e i flag richiesti. Se impostato, il comando che esegui sarà concatenato come argomento. Questo si applica sia ai comandi utente che agli hook di eventi.", "globalRules": "Questo è un insieme globale di regole permetti/nega, che si applicano ad ogni utente. Puoi definire regole specifiche per ogni utente, per sovrascrivere queste.", "globalSettings": "Impostazioni globali", "hideDotfiles": "Nascondi dotfile", "insertPath": "Inserisci il percorso", "insertRegex": "Inserisci la regex", "instanceName": "Nome dell'istanza", "language": "<PERSON><PERSON>", "lockPassword": "Impedisci all'utente di modificare la password", "newPassword": "La tua nuova password", "newPasswordConfirm": "Conferma la password", "newUser": "Nuovo utente", "password": "Password", "passwordUpdated": "Password aggiornata!", "path": "<PERSON><PERSON><PERSON>", "perm": {"create": "Creare file e cartelle", "delete": "Eliminare file e cartelle", "download": "Scaricare", "execute": "Eseguire comandi", "modify": "Modificare file", "rename": "Rinominare o spostare file e cartelle", "share": "Condividere file"}, "permissions": "<PERSON><PERSON><PERSON>", "permissionsHelp": "È possibile impostare l'utente come amministratore o scegliere i permessi singolarmente. Se si seleziona \"Amministratore\", tutte le altre opzioni saranno automaticamente assegnate. La gestione degli utenti rimane un privilegio di un amministratore.\n", "profileSettings": "Impostazioni del profilo", "ruleExample1": "impedisci l'accesso a qualsiasi file avente come prefisso un punto\n (ad esempio .git, .gitignore) presente in ogni cartella.\n", "ruleExample2": "blocca l'accesso al file denominato Caddyfile nella radice del campo di applicazione.", "rules": "Regole", "rulesHelp": "Qui è possibile definire una serie di regole e permessi per questo specifico utente. I file bloccati non appariranno negli elenchi e non saranno accessibili dagli utenti. all'utente. Sia regex che i percorsi relativi all'ambito di applicazione degli utenti sono supportati.\n", "scope": "<PERSON><PERSON>", "settingsUpdated": "Impostazioni aggiornate!", "shareDuration": "Durata della condivisione", "shareManagement": "Gestione delle condivisioni", "singleClick": "Usa un singolo click per aprire file e cartelle", "themes": {"dark": "<PERSON><PERSON>", "light": "Chiaro", "title": "<PERSON><PERSON>"}, "user": "Utente", "userCommands": "<PERSON><PERSON><PERSON>", "userCommandsHelp": "Una lista separata dal spazi con i comandi disponibili per questo utente. Example:\n", "userCreated": "Utente creato!", "userDefaults": "Impostazioni predefinite utente", "userDeleted": "Utente eliminato!", "userManagement": "Gestione degli utenti", "userUpdated": "Utente aggiornato!", "username": "Nome utente", "users": "<PERSON><PERSON><PERSON>"}, "sidebar": {"help": "<PERSON><PERSON>", "hugoNew": "<PERSON>", "login": "<PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "myFiles": "I miei file", "newFile": "Nuovo file", "newFolder": "Nuova cartella", "preview": "Anteprima", "settings": "Impostazioni", "signup": "Registrati", "siteSettings": "Impostazioni del sito"}, "success": {"linkCopied": "<PERSON> copiato!"}, "time": {"days": "<PERSON><PERSON><PERSON>", "hours": "Ore", "minutes": "Minuti", "seconds": "Secondi", "unit": "Unità di tempo"}}