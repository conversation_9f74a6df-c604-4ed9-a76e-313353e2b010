{"buttons": {"cancel": "キャンセル", "clear": "クリアー", "close": "閉じる", "copy": "コピー", "copyFile": "ファイルのコピー", "copyToClipboard": "共有リンクをコピー", "copyDownloadLinkToClipboard": "ダウンロードリンクをコピー", "create": "作成", "delete": "削除", "download": "ダウンロード", "file": "ファイル", "folder": "フォルダー", "hideDotfiles": "ドットで始まるファイルを表示しない", "info": "情報", "more": "さらに", "move": "移動", "moveFile": "ファイルの移動", "new": "新規", "next": "次へ", "ok": "OK", "permalink": "パーマリンクを取得", "previous": "前へ", "publish": "公開", "rename": "名前の変更", "replace": "置換する", "reportIssue": "問題を報告", "save": "保存", "schedule": "スケジュール", "search": "検索", "select": "選択", "selectMultiple": "複数選択", "share": "共有", "shell": "シェルの切り替え", "submit": "送信", "switchView": "表示の切り替え", "toggleSidebar": "サイドバーの切り替え", "update": "更新", "upload": "アップロード", "openFile": "ファイルを開く", "continue": "続行"}, "download": {"downloadFile": "ファイルのダウンロード", "downloadFolder": "フォルダーのダウンロード", "downloadSelected": "選択した項目のダウンロード"}, "upload": {"abortUpload": "アップロードをキャンセルしますか？"}, "errors": {"forbidden": "これにアクセスする権限がありません。", "internal": "内部エラーが発生しました。", "notFound": "リソースが見つかりませんでした。", "connection": "サーバーに接続できませんでした。"}, "files": {"body": "本文", "clear": "消去", "closePreview": "プレビューを閉じる", "files": "ファイル", "folders": "フォルダー", "home": "ホーム", "lastModified": "更新日時", "loading": "読み込み中…", "lonely": "ここには何もないようです…", "metadata": "メタデータ", "multipleSelectionEnabled": "複数選択が有効になっています", "name": "名前", "size": "サイズ", "sortByLastModified": "更新日時で並べ替え", "sortByName": "名前で並べ替え", "sortBySize": "サイズで並べ替え", "noPreview": "プレビューはこのファイルでは利用できません"}, "help": {"click": "ファイルやフォルダーを選択", "ctrl": {"click": "複数のファイルやフォルダーを選択", "f": "検索画面を開く", "s": "現在のフォルダーにあるファイルを保存またはダウンロード"}, "del": "選択した項目を削除", "doubleClick": "ファイルやフォルダーを開く", "esc": "選択を解除／ダイアログを閉じる", "f1": "ヘルプを表示", "f2": "ファイルの名前を変更", "help": "ヘルプ"}, "login": {"createAnAccount": "アカウントを作成", "loginInstead": "ログインする", "password": "パスワード", "passwordConfirm": "パスワード（確認用）", "passwordsDontMatch": "パスワードが一致しません", "signup": "アカウント作成", "submit": "ログイン", "username": "ユーザー名", "usernameTaken": "ユーザー名はすでに取得されています", "wrongCredentials": "ユーザー名またはパスワードが間違っています"}, "permanent": "永久", "prompts": {"copy": "コピー", "copyMessage": "ファイルをコピーする場所を選択してください：", "currentlyNavigating": "現在閲覧しているディレクトリ：", "deleteMessageMultiple": "{count} 個のファイルを削除してもよろしいですか？", "deleteMessageSingle": "このファイル／フォルダーを削除してもよろしいですか？", "deleteMessageShare": "共有中のファイル（{path}）を削除してもよろしいですか？", "deleteTitle": "ファイルの削除", "displayName": "表示名：", "download": "ファイルのダウンロード", "downloadMessage": "ダウンロードする際の圧縮形式を選んでください：", "error": "エラーが発生しました", "fileInfo": "ファイル情報", "filesSelected": "{count} 個のファイル／フォルダーが選択されています", "lastModified": "更新日時", "move": "移動", "moveMessage": "ファイル／フォルダーの新しいハウスを選択してください：", "newArchetype": "archetype に基づいて新しい投稿を作成します。ファイルは content フォルダーに作成されます。", "newDir": "新規フォルダー", "newDirMessage": "フォルダーの名前を入力してください：", "newFile": "新規ファイル", "newFileMessage": "ファイルの名前を入力してください：", "numberDirs": "ディレクトリの数", "numberFiles": "ファイルの数", "rename": "名前変更", "renameMessage": "変更後のファイルの名前を入力してください", "replace": "ファイルの置き換え", "replaceMessage": "アップロードしようとしているファイルと既存のファイルの名前が重複しています。既存のものを置き換えずにアップロードを続けるか、既存のものを置き換えますか？\n", "schedule": "スケジュール", "scheduleMessage": "この投稿の公開予定日時を選んでください。", "show": "表示", "size": "サイズ", "upload": "アップロード", "uploadFiles": "{files} 個のファイルをアップロードしています…", "uploadMessage": "アップロードするオプションを選択してください。", "optionalPassword": "パスワード（オプション）"}, "search": {"images": "画像", "music": "音楽", "pdf": "PDF", "pressToSearch": "エンターを押して検索します", "search": "検索", "typeToSearch": "検索の種類", "types": "ファイルの種類", "video": "動画"}, "settings": {"admin": "管理者", "administrator": "管理者", "allowCommands": "コマンドの実行", "allowEdit": "ファイルやフォルダーの編集、名前の変更、削除", "allowNew": "ファイルやフォルダーの新規作成", "allowPublish": "新しい投稿やページの公開", "allowSignup": "ユーザーの新規登録を許可", "avoidChanges": "（変更しない場合は空白のままにしてください）", "branding": "ブランディング", "brandingDirectoryPath": "ブランディングのディレクトリへのパス", "brandingHelp": "インスタンスの名前の変更、ロゴの変更、カスタムスタイルの追加、GitHub への外部リンクの無効化など、File Browser の見た目や使い勝手をカスタマイズすることができます。\nカスタムブランディングの詳細については、{0}をご覧ください。", "changePassword": "パスワードの変更", "commandRunner": "コマンドランナー", "commandRunnerHelp": "ここでは、指定したイベントの際に実行されるコマンドを設定することができます。1行に1つずつ書く必要があります。環境変数として {0} や {1} が使用可能で、{0} は {1} に関連した変数として扱われます。この機能と使用可能な環境変数の詳細については、{2}をお読みください。", "commandsUpdated": "コマンドを更新しました！", "createUserDir": "新規ユーザー追加時にユーザーのホームディレクトリを自動生成する", "tusUploads": "チャンクされたファイルアップロード", "tusUploadsHelp": "File Browser はチャンクされたファイルアップロードをサポートしており、信頼性の低いネットワーク上でも、効率的で信頼性の高い、再開可能なチャンクされたファイルアップロードを作成することができます。", "tusUploadsChunkSize": "1チャンクあたりのリクエストの最大サイズ。バイト数を示す整数か、10MB、1GBなどの文字列を入力できます。", "tusUploadsRetryCount": "チャンクのアップロードに失敗した場合の再試行回数。", "userHomeBasePath": "ユーザーのホームディレクトリのベースパス", "userScopeGenerationPlaceholder": "スコープは自動生成されます", "createUserHomeDirectory": "ユーザーのホームディレクトリを作成する", "customStylesheet": "カスタムスタイルシート", "defaultUserDescription": "これらは新規ユーザーのデフォルト設定です。", "disableExternalLinks": "外部リンクを無効にする（ドキュメントへのリンクを除く）", "disableUsedDiskPercentage": "ディスク使用率のグラフを無効にする", "documentation": "ドキュメント", "examples": "例", "executeOnShell": "シェルで実行する", "executeOnShellDescription": "デフォルトでは、File Browser はバイナリを直接呼び出してコマンドを実行します。代わりにシェル（Bash や PowerShell など）で実行したい場合は、必要な引数やフラグをここで指定します。値が指定されている場合、実行するコマンドが引数として追加されます。これは、ユーザーコマンドとイベントフックの両方に適用されます。", "globalRules": "これはグローバルな許可と不許可のルールセットです。これはすべてのユーザーに適用されます。ユーザーごとに特定のルールを設定することで、これらのルールを上書きすることができます。", "globalSettings": "グローバル設定", "hideDotfiles": "ドットで始まるファイルを表示しない", "insertPath": "パスを入力してください", "insertRegex": "正規表現を入力してください", "instanceName": "インスタンス名", "language": "言語", "lockPassword": "ユーザーがパスワードを変更できないようにする", "newPassword": "新しいパスワード", "newPasswordConfirm": "新しいパスワード（再入力）", "newUser": "新規ユーザー作成", "password": "パスワード", "passwordUpdated": "パスワードを更新しました！", "path": "パス", "perm": {"create": "ファイルやフォルダーの作成", "delete": "ファイルやフォルダーの削除", "download": "ダウンロード", "execute": "コマンドの実行", "modify": "ファイルの編集", "rename": "ファイルやフォルダーの編集・移動", "share": "ファイルの共有"}, "permissions": "権限", "permissionsHelp": "ユーザーを管理者に設定するか、その他の権限を個別に選択することができます。「管理者」を選択すると、他のオプションはすべて自動的にチェックされます。ユーザーを管理するには管理者権限が必要です。\n", "profileSettings": "プロフィール設定", "ruleExample1": ".git や .gitignore のようなドットから始まるファイルへのアクセスを禁止します。\n", "ruleExample2": "スコープのルートにある Caddyfile という名前のファイルへのアクセスを禁止します。", "rules": "ルール", "rulesHelp": "ここでは、特定のユーザーに対して許可と不許可のルールを設定することができます。ブロックされたファイルはリストに表示されず、ユーザはアクセスできなくなります。正規表現とユーザースコープからの相対パスをサポートしています。\n", "scope": "スコープ", "setDateFormat": "正確な日時表記を使用する", "settingsUpdated": "設定を更新しました！", "shareDuration": "共有期間", "shareManagement": "共有の管理", "shareDeleted": "ファイルの共有を削除しました！", "singleClick": "ダブルクリックの代わりにクリックでファイルやフォルダーを開く", "themes": {"dark": "ダーク", "light": "ライト", "title": "テーマ"}, "user": "ユーザー", "userCommands": "コマンド", "userCommandsHelp": "このユーザーが使用可能なコマンドをスペースで区切ったリスト。例:\n", "userCreated": "ユーザーを作成しました！", "userDefaults": "ユーザーのデフォルト設定", "userDeleted": "ユーザーを削除しました！", "userManagement": "ユーザー管理", "userUpdated": "ユーザーを更新しました！", "username": "ユーザー名", "users": "ユーザー"}, "sidebar": {"help": "ヘルプ", "hugoNew": "<PERSON>", "login": "ログイン", "logout": "ログアウト", "myFiles": "マイファイル", "newFile": "新規ファイル", "newFolder": "新規フォルダー", "preview": "プレビュー", "settings": "設定", "signup": "サインアップ", "siteSettings": "サイト設定"}, "success": {"linkCopied": "リンクをコピーしました！"}, "time": {"days": "日", "hours": "時間", "minutes": "分", "seconds": "秒", "unit": "時間の単位"}}