{"buttons": {"cancel": "취소", "clear": "지우기", "close": "닫기", "copy": "복사", "copyFile": "파일 복사", "copyToClipboard": "클립보드 복사", "create": "생성", "delete": "삭제", "download": "다운로드", "hideDotfiles": "숨김파일(dotfile)을 표시 안함", "info": "정보", "more": "더보기", "move": "이동", "moveFile": "파일 이동", "new": "신규", "next": "다음", "ok": "확인", "permalink": "링크 얻기", "previous": "이전", "publish": "게시", "rename": "이름 바꾸기", "replace": "대체", "reportIssue": "이슈 보내기", "save": "저장", "schedule": "일정", "search": "검색", "select": "선택", "selectMultiple": "다중 선택", "share": "공유", "shell": "쉘 전환", "switchView": "보기 전환", "toggleSidebar": "사이드바 전환", "update": "업데이트", "upload": "업로드"}, "download": {"downloadFile": "파일 다운로드", "downloadFolder": "폴더 다운로드", "downloadSelected": "선택 항목 다운로드"}, "errors": {"forbidden": "접근 권한이 없습니다.", "internal": "오류가 발생하였습니다.", "notFound": "해당 경로를 찾을 수 없습니다."}, "files": {"body": "본문", "closePreview": "미리보기 닫기", "files": "파일", "folders": "폴더", "home": "홈", "lastModified": "최종 수정", "loading": "로딩중...", "lonely": "폴더가 비어 있습니다...", "metadata": "메타데이터", "multipleSelectionEnabled": "다중 선택 켜짐", "name": "이름", "size": "크기", "sortByLastModified": "수정시간순 정렬", "sortByName": "이름순", "sortBySize": "크기순"}, "help": {"click": "파일이나 디렉토리를 선택해주세요.", "ctrl": {"click": "여러 개의 파일이나 디렉토리를 선택해주세요.", "f": "검색창 열기", "s": "파일 또는 디렉토리 다운로드"}, "del": "선택된 파일 삭제", "doubleClick": "파일 또는 디렉토리 열기", "esc": "선택 취소/프롬프트 닫기", "f1": "정보", "f2": "파일 이름 변경", "help": "도움말"}, "login": {"createAnAccount": "계정 생성", "loginInstead": "이미 계정이 있습니다", "password": "비밀번호", "passwordConfirm": "비밀번호 확인", "passwordsDontMatch": "비밀번호가 일치하지 않습니다", "signup": "가입하기", "submit": "로그인", "username": "사용자 이름", "usernameTaken": "사용자 이름이 존재합니다", "wrongCredentials": "사용자 이름 또는 비밀번호를 확인하십시오"}, "permanent": "영구", "prompts": {"copy": "복사", "copyMessage": "복사할 디렉토리:", "currentlyNavigating": "현재 위치:", "deleteMessageMultiple": "{count} 개의 파일을 삭제하시겠습니까?", "deleteMessageSingle": "파일 혹은 디렉토리를 삭제하시겠습니까?", "deleteTitle": "파일 삭제", "displayName": "게시 이름:", "download": "파일 다운로드", "downloadMessage": "다운로드 포맷 설정.", "error": "에러 발생!", "fileInfo": "파일 정보", "filesSelected": "{count} 개의 파일이 선택되었습니다.", "lastModified": "최종 수정", "move": "이동", "moveMessage": "이동할 화일 또는 디렉토리를 선택하세요:", "newArchetype": "원형을 유지하는 포스트를 생성합니다. 파일은 컨텐트 폴더에 생성됩니다.", "newDir": "새 디렉토리", "newDirMessage": "새 디렉토리 이름을 입력해주세요.", "newFile": "새 파일", "newFileMessage": "새 파일 이름을 입력해주세요.", "numberDirs": "디렉토리 수", "numberFiles": "파일 수", "rename": "이름 변경", "renameMessage": "새로운 이름을 입력하세요.", "replace": "대체하기", "replaceMessage": "동일한 파일 이름이 존재합니다. 현재 파일을 덮어쓸까요?\n", "schedule": "일정", "scheduleMessage": "이 글을 공개할 시간을 알려주세요.", "show": "보기", "size": "크기", "upload": "업로드", "uploadMessage": "업로드 옵션을 선택하세요."}, "search": {"images": "이미지", "music": "음악", "pdf": "PDF", "pressToSearch": "검색하려면 엔터를 입력하세요", "search": "검색...", "typeToSearch": "검색어 입력...", "types": "Types", "video": "비디오"}, "settings": {"admin": "관리자", "administrator": "관리자", "allowCommands": "명령 실행", "allowEdit": "파일/디렉토리의 수정/변경/삭제 허용", "allowNew": "파일/디렉토리 생성 허용", "allowPublish": "새 포스트/페이지 생성 허용", "allowSignup": "사용자 가입 허용", "avoidChanges": "(수정하지 않으면 비워두세요)", "branding": "브랜딩", "brandingDirectoryPath": "브랜드 디렉토리 경로", "brandingHelp": "File Browser 인스턴스는 이름, 로고, 스타일 등을 변경할 수 있습니다. 자세한 사항은 여기{0}에서 확인하세요.", "changePassword": "비밀번호 변경", "commandRunner": "명령 실행기", "commandRunnerHelp": "이벤트에 해당하는 명령을 설정하세요. 줄당 1개의 명령을 적으세요. 환경 변수{0} 와 {1}이 사용가능하며,  {0} 은 {1}에 상대 경로 입니다. 자세한 사항은 {2} 를 참조하세요.", "commandsUpdated": "명령 수정됨!", "createUserDir": "Auto create user home dir while adding new user", "customStylesheet": "커스텀 스타일시트", "defaultUserDescription": "아래 사항은 신규 사용자들에 대한 기본 설정입니다.", "disableExternalLinks": "외부 링크 감추기", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "문서", "examples": "예", "executeOnShell": "쉘에서 실행", "executeOnShellDescription": "기본적으로 File Browser 는 바이너리를 명령어로 호출하여 실행합니다. 쉘을 통해 실행하기를 원한다면, Bash 또는 PowerShell 에 필요한 인수와 플래그를 설정하세요. 사용자 명령어와 이벤트 훅에 모두 적용됩니다.", "globalRules": "규칙에 대한 전역설정으로 모든 사용자에게 적용됩니다. 지정된 규칙은 사용자 설정을 덮어쓰기 합니다.", "globalSettings": "전역 설정", "hideDotfiles": "숨김파일(dotfile)을 표시하지 않습니다.", "insertPath": "경로 입력", "insertRegex": "정규식 입력", "instanceName": "인스턴스 이름", "language": "언어", "lockPassword": "사용자에 의한 비밀번호 변경을 허용하지 않음", "newPassword": "새로운 비밀번호", "newPasswordConfirm": "새로운 비밀번호 확인", "newUser": "새로운 사용자", "password": "비밀번호", "passwordUpdated": "비밀번호 수정 완료!", "path": "경로", "perm": {"create": "파일이나 디렉토리 생성하기", "delete": "화일이나 디렉토리 삭제하기", "download": "다운로드", "execute": "명령 실행", "modify": "파일 편집", "rename": "파일 이름 변경 또는 디렉토리 이동", "share": "파일 공유하기"}, "permissions": "권한", "permissionsHelp": "사용자를 관리자로 만들거나 권한을 부여할 수 있습니다. 관리자를 선택하면, 모든 옵션이 자동으로 선택됩니다. 사용자 관리는 현재 관리자만 할 수 있습니다.\n", "profileSettings": "프로필 설정", "ruleExample1": "점(.)으로 시작하는 모든 파일의 접근을 방지합니다.(예 .git, .gitignore)\n", "ruleExample2": "Caddyfile파일의 접근을 방지합니다.", "rules": "룰", "rulesHelp": "사용자별로 규칙을 허용/방지를 지정할 수 있습니다. 방지된 파일은 보이지 않고 사용자들은 접근할 수 없습니다. 사용자의 접근 허용 범위와 관련해 정규표현식(regex)과 경로를 지원합니다.\n", "scope": "범위", "settingsUpdated": "설정 수정됨!", "shareDuration": "공유 기간", "shareManagement": "공유 내역 관리", "singleClick": "한번 클릭으로 파일과 폴더를 열도록 합니다.", "themes": {"dark": "다크테마", "light": "라이트테마", "title": "테마"}, "user": "사용자", "userCommands": "명령어", "userCommandsHelp": "사용에게 허용할 명령어를 공백으로 구분하여 입력하세요. 예:\n", "userCreated": "사용자 생성됨!", "userDefaults": "사용자 기본 설정", "userDeleted": "사용자 삭제됨!", "userManagement": "사용자 관리", "userUpdated": "사용자 수정됨!", "username": "사용자 이름", "users": "사용자"}, "sidebar": {"help": "도움말", "hugoNew": "<PERSON>", "login": "로그인", "logout": "로그아웃", "myFiles": "내 파일", "newFile": "새로운 파일", "newFolder": "새로운 폴더", "preview": "미리보기", "settings": "설정", "signup": "가입하기", "siteSettings": "사이트 설정"}, "success": {"linkCopied": "링크가 복사되었습니다!"}, "time": {"days": "일", "hours": "시", "minutes": "분", "seconds": "초", "unit": "Time Unit"}}