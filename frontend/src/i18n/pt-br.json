{"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copyFile": "Copiar arquivo", "copyToClipboard": "Copiar", "create": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "download": "Baixar", "file": "Arquivo", "folder": "Pasta", "hideDotfiles": "Ocultar dotfiles", "info": "Informações", "more": "<PERSON><PERSON>", "move": "Mover", "moveFile": "Mover arquivo", "new": "Novo", "next": "Próximo", "ok": "OK", "permalink": "Obter link permanente", "previous": "Anterior", "publish": "Publicar", "rename": "Renomear", "replace": "Substituir", "reportIssue": "<PERSON><PERSON>ar erro", "save": "<PERSON><PERSON>", "schedule": "Agendar", "search": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "selectMultiple": "Se<PERSON><PERSON><PERSON>", "share": "Compartilhar", "shell": "Alternar console", "submit": "Enviar", "switchView": "Alterar modo de visão", "toggleSidebar": "Alternar barra lateral", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "Enviar", "openFile": "Abrir"}, "download": {"downloadFile": "Baixar arquivo", "downloadFolder": "Baixar pasta", "downloadSelected": "Baixar selecionado"}, "errors": {"forbidden": "Você não tem permissões para acessar isto.", "internal": "Ops! Algum erro ocorreu.", "notFound": "Ops! Nada foi encontrado.", "connection": "O servidor não pode ser alcançado."}, "files": {"body": "Corpo", "closePreview": "Fechar pré-visualização", "files": "<PERSON>r<PERSON><PERSON>", "folders": "Pastas", "home": "Início", "lastModified": "Última modificação", "loading": "Carregando. Aguarde, por favor.", "lonely": "Não existe nada aqui.", "metadata": "Metadados", "multipleSelectionEnabled": "Se<PERSON>ção múltipla ativada", "name": "Nome", "size": "<PERSON><PERSON><PERSON>", "sortByLastModified": "Ordenar pela última modificação", "sortByName": "Ordenar pelo nome", "sortBySize": "Ordenar pelo tamanho", "noPreview": "Pré-visualização não disponível para este arquivo."}, "help": {"click": "selecionar pasta ou arquivo", "ctrl": {"click": "selecionar várias pastas e arquivos", "f": "pesquisar", "s": "salvar um arquivo ou baixar a pasta que você está"}, "del": "apagar os arquivos selecionados", "doubleClick": "abrir pasta ou arquivo", "esc": "limpar seleção e/ou fechar menu", "f1": "esta informação", "f2": "renomear arquivo", "help": "<PERSON><PERSON><PERSON>"}, "login": {"createAnAccount": "Criar uma conta", "loginInstead": "<PERSON><PERSON> possui uma conta", "password": "<PERSON><PERSON>", "passwordConfirm": "Confirmação de senha", "passwordsDontMatch": "As senhas não coincidem", "signup": "Cadastrar", "submit": "<PERSON><PERSON>", "username": "Nome do usuário", "usernameTaken": "Nome de usuário j<PERSON> existe", "wrongCredentials": "Ops! Dados incorretos."}, "permanent": "Permanente", "prompts": {"copy": "Copiar", "copyMessage": "Escolha um lugar para copiar os arquivos:", "currentlyNavigating": "Navegando em:", "deleteMessageMultiple": "<PERSON><PERSON>a a<PERSON> {count} arquivo(s)?", "deleteMessageSingle": "Deseja apagar esta pasta/arquivo?", "deleteMessageShare": "Deseja apagar este compartilhamento ({path})?", "deleteTitle": "Apagar arquivos", "displayName": "Nome:", "download": "Baixar arquivos", "downloadMessage": "Escolha o formato do arquivo.", "error": "Algo de errado ocorreu", "fileInfo": "Informação do arquivo", "filesSelected": "{count} arquivos selecionados.", "lastModified": "Última modificação", "move": "Mover", "moveMessage": "Escolha uma nova pasta para os seus arquivos:", "newArchetype": "Criar um novo post baseado num \"archetype\". O seu arquivo será criado na pasta \"content\".", "newDir": "Nova pasta", "newDirMessage": "Escreva o nome da nova pasta.", "newFile": "Novo arquivo", "newFileMessage": "Escreva o nome do novo arquivo.", "numberDirs": "Número de pastas", "numberFiles": "Número de arquivos", "rename": "Renomear", "renameMessage": "Insira um novo nome para", "replace": "Substituir", "replaceMessage": "Já existe um arquivo com nome igual a um dos que está tentando enviar. Deseja substituir?\n", "schedule": "Agendar", "scheduleMessage": "Escolha uma data para agendar a publicação deste post.", "show": "Mostrar", "size": "<PERSON><PERSON><PERSON>", "upload": "Enviar", "uploadFiles": "Enviando {files} arquivos...", "uploadMessage": "Selecione uma opção para enviar.", "optionalPassword": "Senha opcional"}, "search": {"images": "Imagens", "music": "Músicas", "pdf": "PDF", "pressToSearch": "Pressione Enter para pesquisar...", "search": "Pesquise...", "typeToSearch": "Digite para pesquisar...", "types": "Tipos", "video": "Vídeos"}, "settings": {"admin": "Admin", "administrator": "Administrador", "allowCommands": "Executar comandos", "allowEdit": "Editar, renomear e apagar arquivos ou pastas", "allowNew": "Criar novos arquivos e pastas", "allowPublish": "Publicar novas páginas e conteúdos", "allowSignup": "Permitir ca<PERSON> de usuário<PERSON>", "avoidChanges": "(deixe em branco para manter)", "branding": "Customização", "brandingDirectoryPath": "Diretório de customização", "brandingHelp": "Você pode mudar a aparência e experiência de sua instância do File Browser alterando seu nome, logotipo, adicionando estilos customizados e até desabilitando links externos para o GitHub.\nPara mais informações sobre customizações, confira {0}.", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "commandRunner": "Execução de comandos", "commandRunnerHelp": "Aqui você pode definir comandos que serão executados nos eventos descritos. Escreva um por linha. As variáveis de ambiente {0} e {1} estão disponíveis, sendo {0} relativo a {1}. Para mais informações sobre esta função e as variáveis de ambiente disponíveis, leia a {2}.", "commandsUpdated": "Comandos atualizados!", "createUserDir": "Criar diretório Home para novos usuários", "userHomeBasePath": "Caminho base para diretórios de usuários", "userScopeGenerationPlaceholder": "O escopo será gerado automaticamente", "createUserHomeDirectory": "Criar diretório Home de usuário", "customStylesheet": "Estilos personalizados", "defaultUserDescription": "<PERSON><PERSON><PERSON> as configurações padrão para novos usuários.", "disableExternalLinks": "Desabilitar links externos (exceto documentação)", "disableUsedDiskPercentage": "Desabilitar gráfico de porcentagem de disco usado", "documentation": "documentação", "examples": "Exemplos", "executeOnShell": "Executar no console", "executeOnShellDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, o File Browser executa os comandos chamando os binários diretamente. Se ao invés disso desejar executá-los em um console (como Bash ou PowerShell), você pode defini-los aqui com os argumentos e flags necessários. Se definido, o comando que executar será acrescentado como um argumento. Isto se aplica a comandos de usuário e eventos hook.", "globalRules": "Este é um conjunto global de regras de permissão e restrição que se aplicam a todos os usuários. Você pode definir regras específicas em cada usuário para sobrepor estas.", "globalSettings": "Configurações globais", "hideDotfiles": "Ocultar dotfiles", "insertPath": "Inserir o caminho", "insertRegex": "Inserir expressão regular", "instanceName": "Nome da instância", "language": "Idioma", "lockPassword": "Não permitir que o usuário altere a senha", "newPassword": "Nova senha", "newPasswordConfirm": "Confirme a nova senha", "newUser": "Novo usuário", "password": "<PERSON><PERSON>", "passwordUpdated": "Senha atualizada!", "path": "", "perm": {"create": "Criar arquivos e diretórios", "delete": "Apagar arquivos e diretórios", "download": "Baixar", "execute": "Executar comandos", "modify": "<PERSON><PERSON>", "rename": "Renomear ou mover arquivos e diretórios", "share": "Compartilhar arquivos"}, "permissions": "Permissões", "permissionsHelp": "Pode definir o usuário como administrador ou escolher as permissões manualmente. Se selecionar a opção \"Administrador\", todas as outras opções serão automaticamente selecionadas. A gestão dos usuários é um privilégio restringido aos administradores.\n", "profileSettings": "Configurações do usuário", "ruleExample1": "previne o acesso a qualquer \"dotfile\" (como .git, .gitignore) em qualquer pasta\n", "ruleExample2": "bloqueia o acesso ao arquivo chamado Caddyfile.", "rules": "Regras", "rulesHelp": "Aqui você pode definir um conjunto de regras para permitir ou bloquear o acesso do usuário a determinados arquivos ou pastas. Os arquivos bloqueados não irão aparecer durante a navegação. Suportamos expressões regulares e os caminhos dos arquivos devem ser relativos à base do usuário.\n", "scope": "Escopo", "setDateFormat": "Definir formato exato de data", "settingsUpdated": "Configurações atualizadas!", "shareDuration": "Duração do compartilhamento", "shareManagement": "Gerenciamento do compartilhamento", "shareDeleted": "Compartilhamento a<PERSON>gado!", "singleClick": "Usar clique único para abrir arquivos e diretórios", "themes": {"dark": "Escuro", "light": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>"}, "user": "<PERSON><PERSON><PERSON><PERSON>", "userCommands": "<PERSON><PERSON><PERSON>", "userCommandsHelp": "Uma lista, separada com espaços, de comandos disponíveis para este usuário. Exemplo:", "userCreated": "<PERSON>u<PERSON>rio criado!", "userDefaults": "Configurações padrão de usuário", "userDeleted": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>!", "userManagement": "Gerenciamento de usuários", "userUpdated": "Usuário atualizado!", "username": "Nome do usuário", "users": "Usuários"}, "sidebar": {"help": "<PERSON><PERSON><PERSON>", "hugoNew": "<PERSON>", "login": "<PERSON><PERSON>", "logout": "<PERSON><PERSON>", "myFiles": "<PERSON>r<PERSON><PERSON>", "newFile": "Novo arquivo", "newFolder": "Nova pasta", "preview": "Pré-visualizar", "settings": "Configurações", "signup": "Cadastrar", "siteSettings": "Configurações do site"}, "success": {"linkCopied": "Link copiado!"}, "time": {"days": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "unit": "Unidades de Tempo"}}