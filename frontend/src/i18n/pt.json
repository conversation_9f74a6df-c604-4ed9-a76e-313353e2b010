{"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "clear": "Limpar", "close": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copyFile": "<PERSON><PERSON><PERSON>", "copyToClipboard": "Copiar", "create": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "download": "<PERSON><PERSON><PERSON><PERSON>", "hideDotfiles": "", "info": "Info", "more": "<PERSON><PERSON>", "move": "Mover", "moveFile": "Mover <PERSON><PERSON><PERSON>", "new": "Novo", "next": "Próximo", "ok": "OK", "permalink": "Obter link permanente", "previous": "Anterior", "publish": "Publicar", "rename": "Alterar nome", "replace": "Substituir", "reportIssue": "Reportar erro", "save": "Guardar", "schedule": "Agendar", "search": "<PERSON><PERSON><PERSON><PERSON>", "select": "Selecionar", "selectMultiple": "Selecionar vários", "share": "Partilhar", "shell": "Alternar shell", "switchView": "Alterar vista", "toggleSidebar": "Alternar barra lateral", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "Enviar"}, "download": {"downloadFile": "<PERSON><PERSON><PERSON><PERSON>", "downloadFolder": "Descarregar pasta", "downloadSelected": ""}, "errors": {"forbidden": "Não tem permissões para aceder a isto", "internal": "Algo correu bastante mal.", "notFound": "Esta localização não é alcançável."}, "files": {"body": "Corpo", "closePreview": "Fechar pré-visualização", "files": "<PERSON><PERSON><PERSON><PERSON>", "folders": "Pastas", "home": "Início", "lastModified": "Última alteração", "loading": "A carregar...", "lonely": "Sinto-me sozinho...", "metadata": "Metadados", "multipleSelectionEnabled": "Se<PERSON>ção múltipla ativada", "name": "Nome", "size": "<PERSON><PERSON><PERSON>", "sortByLastModified": "Ordenar pela última alteração", "sortByName": "Ordenar pelo nome", "sortBySize": "Ordenar pelo tamanho"}, "help": {"click": "selecionar pasta ou ficheiro", "ctrl": {"click": "selecionar várias pastas e ficheiros", "f": "pesquisar", "s": "guardar um ficheiro ou descarrega a pasta em que está a navegar"}, "del": "eliminar os ficheiros selecionados", "doubleClick": "abrir pasta ou ficheiro", "esc": "limpar seleção e/ou fechar menu", "f1": "esta informação", "f2": "alterar nome do ficheiro", "help": "<PERSON><PERSON><PERSON>"}, "login": {"createAnAccount": "Criar uma conta", "loginInstead": "<PERSON><PERSON> tenho uma conta", "password": "Palavra-passe", "passwordConfirm": "Confirmação da palavra-passe", "passwordsDontMatch": "As palavras-passe não coincidem", "signup": "Registar", "submit": "Entrar na conta", "username": "Nome de utilizador", "usernameTaken": "O nome de utilizador já está registado", "wrongCredentials": "Dados er<PERSON>"}, "permanent": "Permanente", "prompts": {"copy": "Copiar", "copyMessage": "Escolha um lugar para onde copiar os ficheiros:", "currentlyNavigating": "A navegar em:", "deleteMessageMultiple": "Quer eliminar {count} fi<PERSON><PERSON>(s)?", "deleteMessageSingle": "Quer eliminar esta pasta/ficheiro?", "deleteTitle": "Eliminar <PERSON><PERSON><PERSON>s", "displayName": "Nome:", "download": "<PERSON><PERSON><PERSON><PERSON>", "downloadMessage": "Escolha o formato do ficheiro que quer descarregar.", "error": "Algo correu mal", "fileInfo": "Informação do ficheiro", "filesSelected": "{count} ficheiros selecionados.", "lastModified": "Última alteração", "move": "Mover", "moveMessage": "Escolha uma nova casa para os seus ficheiros/pastas:", "newArchetype": "Criar um novo post baseado num \"archetype\". O seu ficheiro será criado na pasta \"content\".", "newDir": "Nova pasta", "newDirMessage": "Escreva o nome da nova pasta.", "newFile": "Novo ficheiro", "newFileMessage": "Escreva o nome do novo ficheiro.", "numberDirs": "Número de pastas", "numberFiles": "Número de <PERSON>iros", "rename": "Alterar nome", "renameMessage": "Insira um novo nome para", "replace": "Substituir", "replaceMessage": "Já existe um ficheiro com nome igual a um dos que está a tentar enviar. Quer substituí-lo?\n", "schedule": "Agendar", "scheduleMessage": "Escolha uma data para publicar este post.", "show": "Mostrar", "size": "<PERSON><PERSON><PERSON>", "upload": "", "uploadMessage": ""}, "search": {"images": "Imagens", "music": "Música", "pdf": "PDF", "pressToSearch": "Tecla Enter para pesquisar...", "search": "Pesquisar...", "typeToSearch": "Escrever para pesquisar...", "types": "Tipos", "video": "Vídeos"}, "settings": {"admin": "Admin", "administrator": "Administrador", "allowCommands": "Executar comandos", "allowEdit": "Editar, renomear e eliminar ficheiros ou pastas", "allowNew": "Criar novos ficheiros e pastas", "allowPublish": "Publicar novas páginas e conteúdos", "allowSignup": "Permitir que os utilizadores criem contas", "avoidChanges": "(deixe em branco para manter)", "branding": "<PERSON><PERSON>", "brandingDirectoryPath": "<PERSON>in<PERSON> da pasta de marca", "brandingHelp": "Pode personalizar a aparência do seu Navegador de Ficheiros, alterar o nome, substituindo o logótipo, adicionando estilos personalizados e mesmo desativando links externos para o GitHub.\nPara mais informações sobre marca personalizada, por favor veja {0}.", "changePassword": "Alterar palavra-passe", "commandRunner": "Execução de comandos", "commandRunnerHelp": "Aqui pode definir comandos que são executados nos eventos nomeados. Tem de escrever um por linha. As variáveis de ambiente {0} e {1} estarão disponíveis, sendo {0} relativo a {1}. Para mais informações sobre esta funcionalidade e as variáveis de ambiente, veja {2}.", "commandsUpdated": "Comandos atualizados!", "createUserDir": "Criar automaticamente a pasta de início ao adicionar um novo utilizador", "customStylesheet": "Folha de estilos personalizada", "defaultUserDescription": "<PERSON><PERSON><PERSON> as configurações padrão para novos utilizadores.", "disableExternalLinks": "Desativar links externos (exceto documentação)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "documentação", "examples": "Exemplos", "executeOnShell": "Executar na shell", "executeOnShellDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, o Navegador de Ficheiros executa os comandos chamando os seus binários diretamente. Se em vez disso, quiser executá-los numa shell (como Bash ou PowerShell), pode definir isso aqui com os argumentos e bandeiras necessários. Se definido, o comando que executa será anexado como um argumento. Isto aplica-se tanto a comandos do utilizador como a hooks de eventos.", "globalRules": "Isto é um conjunto global de regras de permissão e negação. Elas aplicam-se a todos os utilizadores. Pode especificar regras específicas para cada configuração do utilizador para sobreporem-se a estas.", "globalSettings": "Configurações globais", "hideDotfiles": "", "insertPath": "Inserir o caminho", "insertRegex": "Inserir expressão regular", "instanceName": "Nome da instância", "language": "Linguagem", "lockPassword": "Não permitir que o utilizador altere a palavra-passe", "newPassword": "Nova palavra-passe", "newPasswordConfirm": "Confirme a nova palavra-passe", "newUser": "Novo utilizador", "password": "Palavra-passe", "passwordUpdated": "Palavra-passe atualizada!", "path": "", "perm": {"create": "Criar ficheiros e pastas", "delete": "Eliminar ficheiros e pastas", "download": "<PERSON><PERSON><PERSON><PERSON>", "execute": "Executar comandos", "modify": "<PERSON><PERSON>", "rename": "Alterar o nome ou mover ficheiros e pastas", "share": "<PERSON><PERSON><PERSON>"}, "permissions": "Permissões", "permissionsHelp": "Pode definir o utilizador como administrador ou escolher as permissões manualmente. Se selecionar a opção \"Administrador\", todas as outras opções serão automaticamente selecionadas. A gestão dos utilizadores é um privilégio restringido aos administradores.\n", "profileSettings": "Configurações do utilizador", "ruleExample1": "previne o acesso a qualquer \"dotfile\" (como .git, .gitignore) em qualquer pasta\n", "ruleExample2": "bloqueia o acesso ao ficheiro chamado Caddyfile na raiz.", "rules": "Regras", "rulesHelp": "Aqui pode definir um conjunto de regras para permitir ou bloquear o acesso do utilizador a determinados ficheiros ou pastas. Os ficheiros bloqueados não irão aparecer durante a navegação. Suportamos expressões regulares e os caminhos dos ficheiros devem ser relativos à base do utilizador.\n", "scope": "Base", "settingsUpdated": "Configurações atualizadas!", "shareDuration": "", "shareManagement": "", "singleClick": "", "themes": {"dark": "", "light": "", "title": ""}, "user": "Utilizador", "userCommands": "<PERSON><PERSON><PERSON>", "userCommandsHelp": "Uma lista, separada com espaços, de comandos disponíveis para este utilizados. Exemplo:", "userCreated": "Utilizador criado!", "userDefaults": "Configurações padrão do utilizador", "userDeleted": "Utilizador eliminado!", "userManagement": "Gestão de utilizadores", "userUpdated": "Utilizador atualizado!", "username": "Nome de utilizador", "users": "Utilizadores"}, "sidebar": {"help": "<PERSON><PERSON><PERSON>", "hugoNew": "<PERSON>", "login": "Entrar", "logout": "<PERSON><PERSON>", "myFiles": "<PERSON><PERSON>", "newFile": "Novo ficheiro", "newFolder": "Nova pasta", "preview": "Pré-visualizar", "settings": "Configurações", "signup": "Registar", "siteSettings": "Configurações do site"}, "success": {"linkCopied": "Link copiado!"}, "time": {"days": "<PERSON><PERSON>", "hours": "<PERSON><PERSON>", "minutes": "<PERSON><PERSON><PERSON>", "seconds": "<PERSON><PERSON><PERSON>", "unit": "Unidades de tempo"}}