{"buttons": {"cancel": "Отмена", "clear": "Очистить", "close": "Закрыть", "copy": "Копировать", "copyFile": "Скопировать файл", "copyToClipboard": "Скопировать в буфер", "create": "Создать", "delete": "Удалить", "download": "Скачать", "file": "<PERSON>а<PERSON><PERSON>", "folder": "Папка", "hideDotfiles": "Скрыть точечные файлы", "info": "Инфо", "more": "Еще", "move": "Переместить", "moveFile": "Переместить файл", "new": "Новый", "next": "Вперед", "ok": "OK", "permalink": "Получить постоянную ссылку", "previous": "Назад", "publish": "Опубликовать", "rename": "Переименовать", "replace": "Перезаписать", "reportIssue": "Сообщить о проблеме", "save": "Сохранить", "schedule": "Планировка", "search": "Поиск", "select": "Выбрать", "selectMultiple": "Мультивыбор", "share": "Поделиться", "shell": "Командная строка", "submit": "Отправить", "switchView": "Вид", "toggleSidebar": "Боковая панель", "update": "Обновить", "upload": "Загрузить", "openFile": "Открыть файл"}, "download": {"downloadFile": "Скачать файл", "downloadFolder": "Загрузить папку", "downloadSelected": "Скачать выбранное"}, "errors": {"forbidden": "У вас нет прав доступа к этому.", "internal": "Что-то пошло не так.", "notFound": "Неправильная ссылка.", "connection": "Нет подключения к серверу."}, "files": {"body": "Тело", "closePreview": "Закрыть", "files": "Файлы", "folders": "Папки", "home": "Главная", "lastModified": "Последнее изменение", "loading": "Загрузка...", "lonely": "Здесь пусто...", "metadata": "Метаданные", "multipleSelectionEnabled": "Мультивыбор включен", "name": "Имя", "size": "Размер", "sortByLastModified": "Сортировка по дате изменения", "sortByName": "Сортировка по имени", "sortBySize": "Сортировка по размеру", "noPreview": "Предварительный просмотр для этого файла недоступен."}, "help": {"click": "выбрать файл или каталог", "ctrl": {"click": "выбрать несколько файлов или каталогов", "f": "открыть поиск", "s": "скачать файл или текущий каталог"}, "del": "удалить выбранные элементы", "doubleClick": "открыть файл или каталог", "esc": "очистить выделение и/или закрыть окно", "f1": "помощь", "f2": "переименовать файл", "help": "Помощь"}, "login": {"createAnAccount": "Создать аккаунт", "loginInstead": "Уже есть аккаунт", "password": "Пароль", "passwordConfirm": "Подтверждение пароля", "passwordsDontMatch": "Пароли не совпадают", "signup": "Зарегистрироваться", "submit": "Войти", "username": "Имя пользователя", "usernameTaken": "Данное имя пользователя уже занято", "wrongCredentials": "Неверные данные"}, "permanent": "Постоянный", "prompts": {"copy": "Копировать", "copyMessage": "Копировать в:", "currentlyNavigating": "Текущий каталог:", "deleteMessageMultiple": "Удалить эти файлы ({count})?", "deleteMessageSingle": "Удалить этот файл/каталог?", "deleteMessageShare": "Удалить этот общий файл/каталог ({path})?", "deleteTitle": "Удалить файлы", "displayName": "Отображаемое имя:", "download": "Скачать файлы", "downloadMessage": "Выберите формат в котором хотите скачать.", "error": "Ошибка", "fileInfo": "Информация о файле", "filesSelected": "Файлов выбрано: {count}.", "lastModified": "Последнее изменение", "move": "Переместить", "moveMessage": "Переместить в:", "newArchetype": "Создайте новую запись на основе архетипа. Файл будет создан в каталоге.", "newDir": "Новый каталог", "newDirMessage": "Имя нового каталога.", "newFile": "Новый файл", "newFileMessage": "Имя нового файла.", "numberDirs": "Количество каталогов", "numberFiles": "Количество файлов", "rename": "Переименовать", "renameMessage": "Новое имя", "replace": "Заменить", "replaceMessage": "Имя одного из загружаемых файлов совпадает с уже существующим файлом. Вы хотите заменить существующий?\n", "schedule": "Планировка", "scheduleMessage": "Запланировать дату и время публикации.", "show": "Показать", "size": "Размер", "upload": "Загрузить", "uploadMessage": "Выберите вариант для загрузки.", "optionalPassword": "Необязательный пароль"}, "search": {"images": "Изображения", "music": "Музыка", "pdf": "PDF", "pressToSearch": "Нажмите Enter для поиска ...", "search": "Поиск...", "typeToSearch": "Введите имя файла ...", "types": "Типы", "video": "Видео"}, "settings": {"admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "administrator": "Администратор", "allowCommands": "Запуск команд", "allowEdit": "Редактирование, переименование и удаление файлов или каталогов", "allowNew": "Создание новых файлов или каталогов", "allowPublish": "Публикация новых записей и страниц", "allowSignup": "Разрешить пользователям регистрироваться", "avoidChanges": "(оставьте поле пустым, чтобы избежать изменений)", "branding": "Брен<PERSON>инг", "brandingDirectoryPath": "Путь к каталогу брендов", "brandingHelp": "Вы можете настроить внешний вид файлового браузера, изменив его имя, заменив логотип, добавив собственные стили и даже отключив внешние ссылки на GitHub.\nДополнительную информацию о персонализированном брендинге можно найти на странице {0}.", "changePassword": "Изменение пароля", "commandRunner": "Запуск команд", "commandRunnerHelp": "Здесь вы можете установить команды, которые будут выполняться в указанных событиях. Вы должны указать по одной команде в каждой строке. Переменные среды {0} и {1} будут доступны, будучи {0} относительно {1}. Дополнительные сведения об этой функции и доступных переменных среды см. В {2}.", "commandsUpdated": "Команды обновлены!", "createUserDir": "Автоматическое создание домашнего каталога пользователя при добавлении нового пользователя", "customStylesheet": "Свой стиль", "defaultUserDescription": "Это настройки по умолчанию для новых пользователей.", "disableExternalLinks": "Отключить внешние ссылки (кроме документации)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "документация", "examples": "Примеры", "executeOnShell": "Выполнить в командной строке", "executeOnShellDescription": "По умолчанию File Browser выполняет команды, напрямую вызывая их бинарные файлы. Если вы хотите вместо этого запускать их в оболочке (например, Bash или PowerShell), вы можете определить их здесь с необходимыми аргументами и флагами. Если установлено, выполняемая вами команда будет добавлена в качестве аргумента. Это относится как к пользовательским командам, так и к обработчикам событий.", "globalRules": "Это глобальный набор разрешающих и запрещающих правил. Они применимы к каждому пользователю. Вы можете определить определенные правила для настроек каждого пользователя, чтобы переопределить их.", "globalSettings": "Глобальные настройки", "hideDotfiles": "Скрыть точечные файлы", "insertPath": "Вставьте путь", "insertRegex": "Вставить регулярное выражение", "instanceName": "Текущее название программы", "language": "Язык", "lockPassword": "Запретить пользователю менять пароль", "newPassword": "Новый пароль", "newPasswordConfirm": "Повтор нового пароля", "newUser": "Новый пользователь", "password": "Пароль", "passwordUpdated": "Пароль обновлен!", "path": "Путь", "perm": {"create": "Создавать файлы и каталоги", "delete": "Удалять файлы и каталоги", "download": "Скачивать", "execute": "Выполнять команды", "modify": "Редактировать файлы", "rename": "Переименовывать или перемещать файлы и каталоги", "share": "Делиться файлами"}, "permissions": "Права доступа", "permissionsHelp": "Можно настроить пользователя как администратора или выбрать разрешения индивидуально. При выборе \"Администратор\", все остальные параметры будут автоматически выбраны. Управление пользователями - привилегия администратора.\n", "profileSettings": "Настройки профиля", "ruleExample1": "предотвратить доступ к любому скрытому файлу (например: .git, .gitignore) в каждой папке.\n", "ruleExample2": "блокирует доступ к файлу с именем Caddyfile в корневой области.", "rules": "Права", "rulesHelp": "Здесь вы можете определить набор разрешающих и запрещающих правил для этого конкретного пользователь. Блокированные файлы не будут отображаться в списках, и не будут доступны для пользователя. Есть поддержка регулярных выражений и относительных путей.\n", "scope": "Корень", "setDateFormat": "Установить точный формат даты", "settingsUpdated": "Настройки применены!", "shareDuration": "Время расшаренной ссылки", "shareManagement": "Управление расшаренными ссылками", "shareDeleted": "Расшаренная ссылка удалена!", "singleClick": "Открытие файлов и каталогов одним кликом", "themes": {"dark": "Темная", "light": "Светлая", "title": "Тема"}, "user": "Пользователь", "userCommands": "Команды", "userCommandsHelp": "Список команд доступных пользователю, разделенный пробелами. Пример:\n", "userCreated": "Пользователь создан!", "userDefaults": "Настройки пользователя по умолчанию", "userDeleted": "Пользователь удален!", "userManagement": "Управление пользователями", "userUpdated": "Пользователь изменен!", "username": "Имя пользователя", "users": "Пользователи"}, "sidebar": {"help": "Помощь", "hugoNew": "<PERSON>", "login": "Войти", "logout": "Выйти", "myFiles": "Файлы", "newFile": "Новый файл", "newFolder": "Новый каталог", "preview": "Предпросмотр", "settings": "Настройки", "signup": "Зарегистрироваться", "siteSettings": "Настройки сайта"}, "success": {"linkCopied": "Ссылка скопирована!"}, "time": {"days": "<PERSON><PERSON>и", "hours": "<PERSON>а<PERSON>ы", "minutes": "Минуты", "seconds": "Секунды", "unit": "Единица времени"}}