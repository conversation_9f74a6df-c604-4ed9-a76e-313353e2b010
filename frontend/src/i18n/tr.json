{"buttons": {"cancel": "Vazgeç", "clear": "<PERSON><PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "copy": "Kopyala", "copyFile": "Dosyayı kopyala", "copyToClipboard": "<PERSON><PERSON> k<PERSON>", "create": "Oluştur", "delete": "Sil", "download": "<PERSON><PERSON><PERSON>", "hideDotfiles": "Nokta dosyalarını gizle", "info": "<PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON> fazla", "move": "Taşı", "moveFile": "Dosyayı taşı", "new": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "ok": "<PERSON><PERSON>", "permalink": "Kalıcı Bağlantı Alın", "previous": "<PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON>ı<PERSON>", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportIssue": "<PERSON><PERSON> bi<PERSON>", "save": "<PERSON><PERSON>", "schedule": "Plan<PERSON>", "search": "Ara", "select": "Seç", "selectMultiple": "Çoklu seçim", "share": "Paylaş", "shell": "Komut satırı aç/kapat", "submit": "<PERSON><PERSON><PERSON>", "switchView": "Görünü<PERSON><PERSON>ştir", "toggleSidebar": "Menüyü aç/kapat", "update": "<PERSON><PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "openFile": "Dosyayı aç"}, "download": {"downloadFile": "Dosyayı indir", "downloadFolder": "Klasörü indir", "downloadSelected": "Seçilileri indir"}, "errors": {"forbidden": "Buna erişim izniniz yok.", "internal": "<PERSON><PERSON> ters gitti.", "notFound": "<PERSON>u konuma ulaşılamıyor.", "connection": "<PERSON><PERSON><PERSON><PERSON>şılamıyor."}, "files": {"body": "Say<PERSON>", "closePreview": "Önizlemeyi kapat", "files": "<PERSON><PERSON><PERSON><PERSON>", "folders": "Klasörler", "home": "<PERSON>", "lastModified": "Son g<PERSON><PERSON>", "loading": "Yükleniyor...", "lonely": "Burada yalnızlık hissediyorum...", "metadata": "meta veri", "multipleSelectionEnabled": "Çoklu seçim etkin", "name": "İsim", "size": "<PERSON><PERSON>", "sortByLastModified": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON> gö<PERSON> s<PERSON>", "sortByName": "<PERSON><PERSON><PERSON>", "sortBySize": "<PERSON><PERSON> gö<PERSON> s<PERSON>", "noPreview": "Bu dosya için önizleme aktif değil"}, "help": {"click": "dosya veya k<PERSON>ö<PERSON>", "ctrl": {"click": "çoklu dosya ve klasör se<PERSON>", "f": "Aramayı aç", "s": "bir dosyayı kaydedin veya bulunduğunuz dizini indirin"}, "del": "seçilileri sil", "doubleClick": "dosya veya dizini açın", "esc": "seç<PERSON><PERSON> temizle veya kapatın", "f1": "bu bilgi", "f2": "dosyayı yeniden adlandır", "help": "Yardım"}, "login": {"createAnAccount": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "loginInstead": "Zaten hesabınız var mı", "password": "Şifre", "passwordConfirm": "Şifre tekrarı", "passwordsDontMatch": "<PERSON><PERSON><PERSON><PERSON>", "signup": "Üye Ol", "submit": "<PERSON><PERSON><PERSON>", "username": "Kullanıcı adı", "usernameTaken": "Kullanıcı adı mevcut", "wrongCredentials": "Yanlış hesap bilgileri"}, "permanent": "Kalıcı", "prompts": {"copy": "Kopyala", "copyMessage": "Dosyalarınızı kopyalayacağınız yeri seçin:", "currentlyNavigating": "<PERSON><PERSON> anki lo<PERSON>yon:", "deleteMessageMultiple": "{count} dosyayı/dosyaları silmek istediğinizden emin misiniz?", "deleteMessageSingle": "Bu dosyayı/klasörü silmek istediğinizden emin misiniz?", "deleteMessageShare": "Bu paylaşımı({path}) silmek istediğinizden emin misiniz?", "deleteTitle": "Dosyaları sil", "displayName": "Görünen Ad:", "download": "Dosyaları indirŞ", "downloadMessage": "İndirmek istediğiniz formatı seçin.", "error": "Bir şeyler yanlış gitti", "fileInfo": "<PERSON><PERSON><PERSON> bi<PERSON>", "filesSelected": "{count} <PERSON><PERSON>.", "lastModified": "Son g<PERSON><PERSON>", "move": "Taşı", "moveMessage": "Do<PERSON>a(lar)ınız/klasör(ler)iniz için yeni ana dizin seçin:", "newArchetype": "Bir prototip temelinde yeni bir gönderi oluşturun. Dosyanız içerik klasöründe oluşturulacaktır.", "newDir": "<PERSON><PERSON>", "newDirMessage": "<PERSON><PERSON> dizinin adını yazın.", "newFile": "<PERSON><PERSON>", "newFileMessage": "<PERSON><PERSON> dos<PERSON>ın adını yazın.", "numberDirs": "Dizin sayısı", "numberFiles": "<PERSON><PERSON><PERSON>", "rename": "<PERSON><PERSON><PERSON>ı<PERSON>", "renameMessage": "i<PERSON>in yeni bir ad girin", "replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replaceMessage": "Yüklemeye çalıştığınız <PERSON>yalardan biri, adı neden<PERSON>yle çakışıyor. Mevcut olanı değiştirmek istiyor musunuz?\n", "schedule": "Plan<PERSON>", "scheduleMessage": "Bu paylaşımın yayınlanmasını planlamak için bir tarih ve saat seçin.", "show": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "uploadMessage": "Yüklemek için bir seçenek belirleyin.", "optionalPassword": "İsteğe bağlı şifre"}, "search": {"images": "<PERSON><PERSON><PERSON><PERSON>", "music": "Müzik", "pdf": "PDF", "pressToSearch": "<PERSON><PERSON><PERSON> i<PERSON>in enter'a basın...", "search": "Ara...", "typeToSearch": "Aramak i<PERSON> ya<PERSON>ın...", "types": "<PERSON><PERSON><PERSON><PERSON>", "video": "Video"}, "settings": {"admin": "Y<PERSON><PERSON><PERSON>", "administrator": "Yönetici", "allowCommands": "Komutları çalıştır", "allowEdit": "Dosyaları veya dizinleri dü<PERSON>in, yeni<PERSON> ad<PERSON>ı<PERSON>ın ve silin", "allowNew": "<PERSON><PERSON> ve dizinler oluşturun", "allowPublish": "Yeni linkler ve sayfaları yayınlayın", "allowSignup": "Kullanıcıların kaydolmasına izin ver", "avoidChanges": "(değişiklikleri önlemek için boş bırakın)", "branding": "<PERSON><PERSON>", "brandingDirectoryPath": "<PERSON><PERSON> di<PERSON> yolu", "brandingHelp": "<PERSON><PERSON><PERSON><PERSON>ek, logo<PERSON> değiştirerek, <PERSON>zel stiller ekleyerek ve hatta GitHub'a harici bağlantıları devre dışı bırakarak Filebrowser örneğinizin görünüşünü ve hissini özelleştirebilirsiniz.\nÖzel marka bilinci oluşturma hakkında daha fazla bilgi için lütfen {0} sayfasına göz atın.", "changePassword": "<PERSON><PERSON><PERSON>", "commandRunner": "<PERSON><PERSON><PERSON>ı<PERSON>ı", "commandRunnerHelp": "<PERSON><PERSON><PERSON>, ad<PERSON><PERSON>r<PERSON><PERSON><PERSON>ş olaylarda yürütülen komutları ayarlayabilirsiniz. Her satıra bir tane yazmalısınız. {0} ve {1} ortam değişkenleri, {1}'ye göre {0} olacak şekilde kullanılabilir olacaktır. Bu özellik ve mevcut ortam değişkenleri hakkında daha fazla bilgi için lütfen {2}'yi okuyun.", "commandsUpdated": "Komu<PERSON>ar g<PERSON>!", "createUserDir": "Kullanıcı <PERSON>en, kullanıcı ana dizinini otomatik oluştur", "customStylesheet": "Özel CSS", "defaultUserDescription": "<PERSON><PERSON>, yeni k<PERSON> i<PERSON><PERSON> a<PERSON>lardı<PERSON>.", "disableExternalLinks": "Harici bağlantıları devre dışı bırakın (dökümantasyon hariç)", "disableUsedDiskPercentage": "Disable used disk percentage graph", "documentation": "dökümantasyon", "examples": "Örnekler", "executeOnShell": "Komut satırında çalıştır", "executeOnShellDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, FileBrowser komutları doğrudan dosyaları çağırarak yürütür. Bunları komut satırında çalıştırmak istiyorsanız (Bash veya PowerShell gibi), burada gerekli argümanlar ve flagler tanımlayabilirsiniz. Ayarlanırsa, y<PERSON><PERSON><PERSON>ttüğünüz komut argüman olarak eklenir. Bu, hem kullanıcı komutları hem de event hooklar için geçerlidir.", "globalRules": "<PERSON><PERSON>, genel bir izin verme ve izin vermeme kurallar bütünüdür. Her kullanıcı için geçerlidirler. Bunları geçersiz kılmak için her kullanıcının ayarlarında belirli kurallar tanımlayabilirsiniz.", "globalSettings": "<PERSON><PERSON>", "hideDotfiles": ". ile ba<PERSON><PERSON>an dosyaları gizle", "insertPath": "<PERSON><PERSON><PERSON>", "insertRegex": "Regex ifadesini ekle", "instanceName": "Instance adı", "language": "Dil", "lockPassword": "Kullanıcının parolayı değiştirmesini engelle", "newPassword": "<PERSON><PERSON>", "newPasswordConfirm": "Yeni şifre tekrarı", "newUser": "<PERSON><PERSON>", "password": "Şifre", "passwordUpdated": "<PERSON><PERSON><PERSON>", "path": "Yol", "perm": {"create": "Do<PERSON>alar ve dizinler oluşturun", "delete": "Do<PERSON><PERSON>r ve dizinleri silin", "download": "<PERSON><PERSON><PERSON>", "execute": "Komutları çalıştır", "modify": "Dosyaları değiştir", "rename": "Dosyaları ve dizinleri yeniden adlandırın veya taşıyın", "share": "Dosyaları paylaş"}, "permissions": "<PERSON><PERSON><PERSON>", "permissionsHelp": "Kullanıcıyı yönetici olarak ayarlayabilir veya izinleri ayrı ayrı seçebilirsiniz. \"Yönetici\"y<PERSON> <PERSON><PERSON><PERSON>, di<PERSON><PERSON> tüm seçenekler otomatik olarak kontrol edilecektir. Kullanıcıların yönetimi, bir yöneticinin yetkisi olarak kalır.\n", "profileSettings": "<PERSON><PERSON>", "ruleExample1": "her klasördeki herhangi bir noktalı dosyaya (.git, .gitignore gibi) eri<PERSON><PERSON><PERSON> engeller.\n", "ruleExample2": "Root erişimidenki CaddyFile dosyalarına erişimi en<PERSON>.", "rules": "<PERSON><PERSON><PERSON>", "rulesHelp": "B<PERSON><PERSON>, bu belirli kullanıcı için bir dizi izin verme ve izin vermeme kuralı tanımlayabilirsiniz. Engellenen dosyalar listelerde görünmeyecek ve kullanıcı bunlara erişemeyecek. Kullanıcı erişimine göre regex ifadeleri destekliyoruz.\n", "scope": "<PERSON><PERSON><PERSON>", "settingsUpdated": "<PERSON><PERSON><PERSON> g<PERSON>!", "shareDuration": "Pay<PERSON>şım sü<PERSON>i", "shareManagement": "<PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON>", "shareDeleted": "<PERSON>laşım silindi!", "singleClick": "Dosyaları ve dizinleri açmak için tek tıklamayı kullanın", "themes": {"dark": "Dark", "light": "Light", "title": "Theme"}, "user": "Kullanıcı", "userCommands": "Komutları", "userCommandsHelp": "Bu kullanıcı için mevcut komutları içeren boşlukla ayrılmış bir liste. Örnek:\n", "userCreated": "Kullanıcı oluşturuldu!", "userDefaults": "Kullanıcı varsayılan ayarları", "userDeleted": "Kullanıcı silindi!", "userManagement": "Kullanıcı yönetimi", "userUpdated": "Kullanıcı güncellendi!", "username": "Kullanıcı adı", "users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sidebar": {"help": "Yardım", "hugoNew": "<PERSON><PERSON>", "login": "<PERSON><PERSON><PERSON>", "logout": "Çıkış", "myFiles": "Dosyalarım", "newFile": "<PERSON><PERSON>", "newFolder": "<PERSON><PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "signup": "<PERSON><PERSON><PERSON>", "siteSettings": "Site ayarları!"}, "success": {"linkCopied": "Link kopyalandı!"}, "time": {"days": "<PERSON><PERSON><PERSON>", "hours": "Saat", "minutes": "Dakika", "seconds": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON>"}}