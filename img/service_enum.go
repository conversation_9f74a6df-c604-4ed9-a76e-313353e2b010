// Code generated by go-enum
// DO NOT EDIT!

package img

import (
	"database/sql/driver"
	"fmt"
)

const (
	// FormatJpeg is a Format of type Jpeg
	FormatJpeg Format = iota
	// FormatPng is a Format of type Png
	FormatPng
	// FormatGif is a Format of type Gif
	FormatGif
	// FormatTiff is a Format of type Tiff
	FormatTiff
	// FormatBmp is a Format of type Bmp
	FormatBmp
)

const _FormatName = "jpegpnggiftiffbmp"

var _FormatMap = map[Format]string{
	0: _FormatName[0:4],
	1: _FormatName[4:7],
	2: _FormatName[7:10],
	3: _FormatName[10:14],
	4: _FormatName[14:17],
}

// String implements the Stringer interface.
func (x Format) String() string {
	if str, ok := _FormatMap[x]; ok {
		return str
	}
	return fmt.Sprintf("Format(%d)", x)
}

var _FormatValue = map[string]Format{
	_FormatName[0:4]:   0,
	_FormatName[4:7]:   1,
	_FormatName[7:10]:  2,
	_FormatName[10:14]: 3,
	_FormatName[14:17]: 4,
}

// ParseFormat attempts to convert a string to a Format
func ParseFormat(name string) (Format, error) {
	if x, ok := _FormatValue[name]; ok {
		return x, nil
	}
	return Format(0), fmt.Errorf("%s is not a valid Format", name)
}

// MarshalText implements the text marshaller method
func (x Format) MarshalText() ([]byte, error) {
	return []byte(x.String()), nil
}

// UnmarshalText implements the text unmarshaller method
func (x *Format) UnmarshalText(text []byte) error {
	name := string(text)
	tmp, err := ParseFormat(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Scan implements the Scanner interface.
func (x *Format) Scan(value interface{}) error {
	var name string

	switch v := value.(type) {
	case string:
		name = v
	case []byte:
		name = string(v)
	case nil:
		*x = Format(0)
		return nil
	}

	tmp, err := ParseFormat(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Value implements the driver Valuer interface.
func (x Format) Value() (driver.Value, error) {
	return x.String(), nil
}

const (
	// QualityHigh is a Quality of type High
	QualityHigh Quality = iota
	// QualityMedium is a Quality of type Medium
	QualityMedium
	// QualityLow is a Quality of type Low
	QualityLow
)

const _QualityName = "highmediumlow"

var _QualityMap = map[Quality]string{
	0: _QualityName[0:4],
	1: _QualityName[4:10],
	2: _QualityName[10:13],
}

// String implements the Stringer interface.
func (x Quality) String() string {
	if str, ok := _QualityMap[x]; ok {
		return str
	}
	return fmt.Sprintf("Quality(%d)", x)
}

var _QualityValue = map[string]Quality{
	_QualityName[0:4]:   0,
	_QualityName[4:10]:  1,
	_QualityName[10:13]: 2,
}

// ParseQuality attempts to convert a string to a Quality
func ParseQuality(name string) (Quality, error) {
	if x, ok := _QualityValue[name]; ok {
		return x, nil
	}
	return Quality(0), fmt.Errorf("%s is not a valid Quality", name)
}

// MarshalText implements the text marshaller method
func (x Quality) MarshalText() ([]byte, error) {
	return []byte(x.String()), nil
}

// UnmarshalText implements the text unmarshaller method
func (x *Quality) UnmarshalText(text []byte) error {
	name := string(text)
	tmp, err := ParseQuality(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Scan implements the Scanner interface.
func (x *Quality) Scan(value interface{}) error {
	var name string

	switch v := value.(type) {
	case string:
		name = v
	case []byte:
		name = string(v)
	case nil:
		*x = Quality(0)
		return nil
	}

	tmp, err := ParseQuality(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Value implements the driver Valuer interface.
func (x Quality) Value() (driver.Value, error) {
	return x.String(), nil
}

const (
	// ResizeModeFit is a ResizeMode of type Fit
	ResizeModeFit ResizeMode = iota
	// ResizeModeFill is a ResizeMode of type Fill
	ResizeModeFill
)

const _ResizeModeName = "fitfill"

var _ResizeModeMap = map[ResizeMode]string{
	0: _ResizeModeName[0:3],
	1: _ResizeModeName[3:7],
}

// String implements the Stringer interface.
func (x ResizeMode) String() string {
	if str, ok := _ResizeModeMap[x]; ok {
		return str
	}
	return fmt.Sprintf("ResizeMode(%d)", x)
}

var _ResizeModeValue = map[string]ResizeMode{
	_ResizeModeName[0:3]: 0,
	_ResizeModeName[3:7]: 1,
}

// ParseResizeMode attempts to convert a string to a ResizeMode
func ParseResizeMode(name string) (ResizeMode, error) {
	if x, ok := _ResizeModeValue[name]; ok {
		return x, nil
	}
	return ResizeMode(0), fmt.Errorf("%s is not a valid ResizeMode", name)
}

// MarshalText implements the text marshaller method
func (x ResizeMode) MarshalText() ([]byte, error) {
	return []byte(x.String()), nil
}

// UnmarshalText implements the text unmarshaller method
func (x *ResizeMode) UnmarshalText(text []byte) error {
	name := string(text)
	tmp, err := ParseResizeMode(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Scan implements the Scanner interface.
func (x *ResizeMode) Scan(value interface{}) error {
	var name string

	switch v := value.(type) {
	case string:
		name = v
	case []byte:
		name = string(v)
	case nil:
		*x = ResizeMode(0)
		return nil
	}

	tmp, err := ParseResizeMode(name)
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

// Value implements the driver Valuer interface.
func (x ResizeMode) Value() (driver.Value, error) {
	return x.String(), nil
}
