package settings

import (
	"time"

	"github.com/puryagh/filebrowser/v2/files"
	"github.com/puryagh/filebrowser/v2/users"
)

// UserDefaults is a type that holds the default values
// for some fields on User.
type UserDefaults struct {
	Scope        string            `json:"scope"`
	Locale       string            `json:"locale"`
	ViewMode     users.ViewMode    `json:"viewMode"`
	SingleClick  bool              `json:"singleClick"`
	Sorting      files.Sorting     `json:"sorting"`
	Perm         users.Permissions `json:"perm"`
	Commands     []string          `json:"commands"`
	HideDotfiles bool              `json:"hideDotfiles"`
	DateFormat   bool              `json:"dateFormat"`
}

// Apply applies the default options to a user.
func (d *UserDefaults) Apply(u *users.User) {
	u.Scope = d.Scope
	u.Locale = d.Locale
	u.ViewMode = d.ViewMode
	u.SingleClick = d.SingleClick
	u.Perm = d.Perm
	u.Sorting = d.Sorting
	u.Commands = d.Commands
	u.HideDotfiles = d.HideDotfiles
	u.DateFormat = d.DateFormat
}

func GetDefaultSettings() *Settings {
	return &Settings{
		Antivirus: struct {
			Enabled    bool          `json:"enabled"`
			ClamAVAddr string        `json:"clamavAddr"`
			Timeout    time.Duration `json:"timeout"`
			Quarantine string        `json:"quarantine"`
			Retention  time.Duration `json:"retention"`
		}{
			Enabled:    false,
			ClamAVAddr: "localhost:3310",
			Timeout:    30 * time.Second,
			Quarantine: "/quarantine",
			Retention:  30 * 24 * time.Hour, // 30 days
		},
	}
}
